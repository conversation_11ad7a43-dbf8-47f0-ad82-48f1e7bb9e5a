package logging

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
)

var Log *zap.SugaredLogger

// SetupZap Initialize the zap instance
//func SetupZap() error {
//	// 5.初始化全局日志句柄，并载入日志钩子处理函数
//	Log = CreateZapFactory(ZapLogHandler)
//	return nil
//}

//func CreateZapFactory(entry func(zapcore.Entry) error) *zap.Logger {
//
//	// 获取程序所处的模式：  开发调试 、 生产
//	//variable.ConfigYml := yml_config.CreateYamlFactory()
//	appDebug := setting.ServerSetting.RunMode
//
//	// 判断程序当前所处的模式，调试模式直接返回一个便捷的zap日志管理器地址，所有的日志打印到控制台即可
//	if appDebug == "debug" {
//		if logger, err := zap.NewDevelopment(zap.Hooks(entry)); err == nil {
//			return logger
//		} else {
//			log.Fatal("创建zap日志包失败，详情：" + err.Error())
//		}
//	}
//
//	// 以下才是 非调试（生产）模式所需要的代码
//	encoderConfig := zap.NewProductionEncoderConfig()
//
//	timePrecision := "second"
//	var recordTimeFormat string
//	switch timePrecision {
//	case "second":
//		recordTimeFormat = "2006-01-02 15:04:05"
//	case "millisecond":
//		recordTimeFormat = "2006-01-02 15:04:05.000"
//	default:
//		recordTimeFormat = "2006-01-02 15:04:05"
//
//	}
//	encoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
//		enc.AppendString(t.Format(recordTimeFormat))
//	}
//	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
//	encoderConfig.TimeKey = "created_at" // 生成json格式日志的时间键字段，默认为 ts,修改以后方便日志导入到 ELK 服务器
//
//	var encoder zapcore.Encoder
//	switch "console" {
//	case "console":
//		encoder = zapcore.NewConsoleEncoder(encoderConfig) // 普通模式
//	case "json":
//		encoder = zapcore.NewJSONEncoder(encoderConfig) // json格式
//	default:
//		encoder = zapcore.NewConsoleEncoder(encoderConfig) // 普通模式
//	}
//
//	////写入器
//	//fileName := variable.BasePath + variable.ConfigYml.GetString("Logs.GoSkeletonLogName")
//
//	// 开始初始化zap日志核心参数，
//	//参数一：编码器
//	//参数二：写入器
//	//参数三：参数级别，debug级别支持后续调用的所有函数写日志，如果是 fatal 高级别，则级别>=fatal 才可以写日志
//	zapCore := zapcore.NewCore(encoder, nil, zap.InfoLevel)
//	return zap.New(zapCore, zap.AddCaller(), zap.Hooks(entry), zap.AddStacktrace(zap.WarnLevel))
//}

func ZapLogHandler(entry zapcore.Entry) error {

	// 参数 entry 介绍
	// entry  参数就是单条日志结构体，主要包括字段如下：
	//Level      日志等级
	//Time       当前时间
	//LoggerName  日志名称
	//Message    日志内容
	//Caller     各个文件调用路径
	//Stack      代码调用栈

	//这里启动一个协程，hook丝毫不会影响程序性能，
	go func(paramEntry zapcore.Entry) {
		//fmt.Println(" GoSkeleton  hook ....，你可以在这里继续处理系统日志....")
		//fmt.Printf("%#+v\n", paramEntry)
	}(entry)
	return nil
}

func SetupZap() {
	now := time.Now()
	infoLogFileName := fmt.Sprintf("%s/info/%04d-%02d-%02d.log", getLogFilePath(), now.Year(), now.Month(), now.Day())
	errorLogFileName := fmt.Sprintf("%s/error/%04d-%02d-%02d.log", getLogFilePath(), now.Year(), now.Month(), now.Day())
	var coreArr []zapcore.Core

	// 获取编码器
	//encoderConfig := zap.NewProductionEncoderConfig()
	//encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder        // 指定时间格式
	//encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder // ，不需要的话取值zapcore.CapitalLevelEncoder就可以了
	////encoderConfig.EncodeCaller = zapcore.FullCallerEncoder        // 显示完整文件路径
	encoderConfig := zapcore.EncoderConfig{
		MessageKey:    "msg",
		LevelKey:      "level",
		TimeKey:       "time",
		NameKey:       "name",
		CallerKey:     "file",
		FunctionKey:   "func",
		StacktraceKey: "stacktrace",
		LineEnding:    zapcore.DefaultLineEnding,
		EncodeLevel:   zapcore.CapitalLevelEncoder,
		EncodeTime: func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
			enc.AppendString(t.Format("2006-01-02 15:04:05"))
		},
		//EncodeTime: zapcore.ISO8601TimeEncoder, // ISO8601 UTC 时间格式
		//EncodeDuration: func(d time.Duration, enc zapcore.PrimitiveArrayEncoder) {
		//	enc.AppendInt64(int64(d) / 1000000)
		//},
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
		//EncodeCaller: zapcore.FullCallerEncoder,
		//EncodeName:       nil,
		//ConsoleSeparator: "",
	}
	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	// 日志级别
	highPriority := zap.LevelEnablerFunc(func(level zapcore.Level) bool {
		return level >= zap.ErrorLevel
	})
	lowPriority := zap.LevelEnablerFunc(func(level zapcore.Level) bool {
		return level < zap.ErrorLevel && level >= zap.DebugLevel
	})

	// info文件writeSyncer
	infoFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename: infoLogFileName, //日志文件存放目录，如果文件夹不存在会自动创建
	})
	// 第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志
	infoFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(infoFileWriteSyncer, zapcore.AddSync(os.Stdout)), lowPriority)

	// error文件writeSyncer
	errorFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename: errorLogFileName, //日志文件存放目录
	})
	// 第三个及之后的参数为写入文件的日志级别,ErrorLevel模式只记录error级别的日志
	errorFileCore := zapcore.NewCore(encoder, zapcore.NewMultiWriteSyncer(errorFileWriteSyncer, zapcore.AddSync(os.Stdout)), highPriority)

	coreArr = append(coreArr, infoFileCore)
	coreArr = append(coreArr, errorFileCore)

	logger := zap.New(zapcore.NewTee(coreArr...), zap.AddCaller())
	Log = logger.Sugar()
	Log.Info("初始化zap日志完成!")
}
