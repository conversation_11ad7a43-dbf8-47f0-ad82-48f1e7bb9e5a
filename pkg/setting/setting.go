package setting

import (
	"log"
	"time"

	"github.com/go-ini/ini"
)

type App struct {
	Vip              string
	ContainerTimeOut time.Duration
	WaitTime         time.Duration
	Host             string
	Port             string
}

var AppSetting = &App{}

type Database struct {
	Driver			string
	User        string
	Password    string
	Host        string
	Port				string
	Name        string
	TablePrefix string
}

var DatabaseSetting = &Database{}

var cfg *ini.File

// Setup initialize the configuration instance
func Setup() {
	var err error
	cfg, err = ini.Load("conf/app.ini")
	if err != nil {
		log.Fatalf("setting.Setup, fail to parse 'conf/app.ini': %v", err)
	}

	mapTo("app", AppSetting)
	mapTo("database", DatabaseSetting)
	log.Println("init config finish!")
}

// mapTo map section
func mapTo(section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("Cfg.MapTo %s err: %v", section, err)
	}
}
