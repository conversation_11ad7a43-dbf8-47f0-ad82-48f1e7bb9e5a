package utils

import "github.com/gin-gonic/gin"


type GinTool struct{
	C *gin.Context
}


type Response struct {
	Code int `json:"code"`
	Data any `json:"data"`
	Msg string `json:"msg"`
}

func (g *GinTool) Response(code int, msg string, data any ) {
	g.C.JSON(200, Response{
		Code: code,
		Data: data,
		Msg: msg,
	})
}


func (g *GinTool) SuccessResponse(msg string, data any) {
	g.C.JSON(200,Response{
		Code: 200,
		Data: data,
		Msg: msg,
	})
}


func (g *GinTool) FailResponse(msg string, data any) {
	g.C.JSON(200,Response{
		Code: 500,
		Data: data,
		Msg: msg,
	})
}

