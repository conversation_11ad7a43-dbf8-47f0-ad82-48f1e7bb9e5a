package utils

import (
	"encoding/xml"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"sync"

	"libvirt.org/go/libvirt"
)

// StorageVolumeInfo 包含存储卷的详细信息
type StorageVolumeInfo struct {
	Name       string                 `json:"name"`
	Capacity   uint64                 `json:"capacity"`
	Allocation uint64                 `json:"allocation"`
	Type       libvirt.StorageVolType `json:"type"`
	Path       string                 `json:"path"`
	Xml        string                 `json:"xml"`
	Format     string                 `json:"format"`
}

type LibvirtManager struct {
	mu        sync.Mutex
	instances map[string]*libvirt.Connect
}

var once sync.Once
var libvirtManager *LibvirtManager

// GetLibvirtManager 返回LibvirtManager 单例实例
func GetLibvirtManager() *LibvirtManager {
	once.Do(func() {
		libvirtManager = &LibvirtManager{
			instances: make(map[string]*libvirt.Connect),
		}
	})

	return libvirtManager
}

// GetLibvirtConnection 返回指定连接标识符的 libvirt 连接实例，如果不存在则创建新的连接
func (lm *LibvirtManager) GetLibvirtConnection(ip string) (*libvirt.Connect, error) {
	lm.mu.Lock()
	defer lm.mu.Unlock()
	uri := GetConnUrl(ip)
	// 如果已经存在连接实例，则直接返回
	if conn, ok := lm.instances[uri]; ok {
		return conn, nil
	}

	// 否则创建新的连接实例
	conn, err := libvirt.NewConnect(uri)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to libvirt at %s: %w", uri, err)
	}

	// 将连接实例存储在 map 中
	lm.instances[uri] = conn

	return conn, nil
}

func GetLibvirtConn(ip string) (*libvirt.Connect, error) {
	l := GetLibvirtManager()
	return l.GetLibvirtConnection(ip)
}

// GetConnUrl 通过ip获取libvirt连接字符串
func GetConnUrl(ip string) string {
	return fmt.Sprintf("qemu+tcp://%s/system", ip)
}

// LibvirtPoolRefresh 刷新存储池
func LibvirtPoolRefresh(conn *libvirt.Connect, pool_name string) error {

	pool, err := conn.LookupStoragePoolByName(pool_name)
	if err != nil {
		fmt.Println("获取存储池失败：", err.Error())
		return err
	}
	defer pool.Free()

	err = pool.Refresh(0)
	if err != nil {
		fmt.Println("存储池刷新失败", err.Error())
		return err
	}

	return nil
}

func LibvirtVolumeInfo(conn *libvirt.Connect, poolName string, volumeName string) (StorageVolumeInfo, error) {
	var storageVolumeInfo StorageVolumeInfo
	// 获取存储池
	pool, err := conn.LookupStoragePoolByName(poolName)
	if err != nil {
		log.Printf("failed to lookup storage pool %s: %v", poolName, err)
		return storageVolumeInfo, err
	}
	defer pool.Free()

	// 获取存储卷
	volume, err := pool.LookupStorageVolByName(volumeName)
	if err != nil {
		log.Printf("failed to lookup storage volume %s: %v", volumeName, err)
		return storageVolumeInfo, err
	}
	defer volume.Free()

	// 获取存储卷信息
	info, err := volume.GetInfo()
	if err != nil {
		log.Printf("failed to get info for storage volume %s: %v", volumeName, err)
		return storageVolumeInfo, err
	}

	// 获取存储卷路径
	path, err := volume.GetPath()
	if err != nil {
		log.Printf("failed to get path for storage volume %s: %v", volumeName, err)
		return storageVolumeInfo, err
	}

	// 获取存储卷的 XML 描述
	xmlDesc, err := volume.GetXMLDesc(0)
	if err != nil {
		log.Fatalf("Failed to get XML description of storage volume: %v", err)
	}

	format := getVolumeFormatFromXML(xmlDesc)

	// 组装存储卷信息到结构体
	storageVolumeInfo = StorageVolumeInfo{
		Name:       volumeName,
		Capacity:   info.Capacity,
		Allocation: info.Allocation,
		Type:       info.Type,
		Path:       path,
		Xml:        xmlDesc,
		Format:     format,
	}
	return storageVolumeInfo, nil
}

// VolumeFormat represents the format of a storage volume
// Volume represents the storage volume XML structure
type VolumeFormat struct {
	XMLName xml.Name `xml:"volume"`
	Type    string   `xml:"type,attr"`
	Target  struct {
		Path   string `xml:"path"`
		Format struct {
			Type string `xml:"type,attr"`
		} `xml:"format"`
	} `xml:"target"`
}

// GetVolumeFormat extracts the volume format from the XML description
func getVolumeFormatFromXML(xmlDesc string) string {

	var volume VolumeFormat
	err := xml.Unmarshal([]byte(xmlDesc), &volume)
	if err != nil {
		log.Fatalf("Failed to unmarshal XML: %v", err)
	}

	if volume.Target.Format.Type != "" {
		return volume.Target.Format.Type
	}
	return "unknown"
}

// HostInfo 包含主机的详细信息
type HostInfo struct {
	IOMMUStatus      string `json:"iommu_status"`
	ConnectionStatus bool   `json:"connection_status"`
	MaintenanceMode  int64  `json:"maintenance_mode"`
	NumaNodes        int64  `json:"numa_nodes"`
	CPUSockets       int64  `json:"cpu_sockets"`
	CorePerSocket    int64  `json:"core_per_socket"`
	ThreadsPerCore   int64  `json:"threads_per_core"`
	ISCSIInitiator   string `json:"iscsi_initiator"`
	SerialNumber     string `json:"serial_number"`
}

// GetHostInfo 获取主机详细信息
func GetHostInfo(conn *libvirt.Connect) (HostInfo, error) {
	info := HostInfo{}

	// 获取主机 XML 描述
	xmlDesc, err := conn.GetCapabilities()
	if err != nil {
		return info, fmt.Errorf("failed to get host capabilities: %w", err)
	}

	// 解析 XML
	type Capabilities struct {
		XMLName xml.Name `xml:"capabilities"`
		Host    struct {
			CPU struct {
				Topology struct {
					Sockets int `xml:"sockets,attr"`
					Cores   int `xml:"cores,attr"`
					Threads int `xml:"threads,attr"`
				} `xml:"topology"`
			} `xml:"cpu"`
			NUMA struct {
				Cells []struct{} `xml:"cells>cell"`
			} `xml:"topology"`
		} `xml:"host"`
	}

	var caps Capabilities
	if err := xml.Unmarshal([]byte(xmlDesc), &caps); err != nil {
		return info, fmt.Errorf("failed to parse capabilities XML: %w", err)
	}

	// 填充信息
	alive, err := conn.IsAlive()
	if err != nil {
		return info, fmt.Errorf("failed to check connection status: %w", err)
	}
	info.ConnectionStatus = alive
	info.NumaNodes = int64(len(caps.Host.NUMA.Cells))
	info.CPUSockets = int64(caps.Host.CPU.Topology.Sockets)
	info.CorePerSocket = int64(caps.Host.CPU.Topology.Cores)
	info.ThreadsPerCore = int64(caps.Host.CPU.Topology.Threads)

	// IOMMU 状态需要通过检查系统文件获取
	iommuEnabled, _ := checkIOMMU()
	info.IOMMUStatus = iommuEnabled

	// Get iSCSI initiator name
	initiatorName, err := getISCSIInitiatorName()
	if err != nil {
		fmt.Printf("Warning: Failed to get iSCSI initiator name: %v", err)
		initiatorName = "" // Set empty if not available
	}
	info.ISCSIInitiator = initiatorName

	serialNumber, err := getSystemSerialNumber()
	if err != nil {
		fmt.Printf("Warning: Failed to get system serial number: %v", err)
		serialNumber = "" // Set empty if not available
	}
	info.SerialNumber = serialNumber

	// 维护模式状态可以通过检查系统标志获取
	maintenanceMode, _ := checkMaintenanceMode()
	info.MaintenanceMode = maintenanceMode

	return info, nil
}

// checkIOMMU 检查 IOMMU 是否启用
func checkIOMMU() (string, error) {
	content, err := os.ReadFile("/sys/class/iommu/*/devices/*/iommu/intel-iommu/cap")
	if err != nil {
		if os.IsNotExist(err) {
			return "false", nil
		}
		return "false", err
	}
	if len(content) > 0 {
		return "true", nil
	}
	return "false", nil
}

// checkMaintenanceMode 检查是否处于维护模式
func checkMaintenanceMode() (int64, error) {
	// 这里需要根据实际系统实现维护模式的检查逻辑
	// 可能需要检查特定文件或服务状态
	return 0, nil
}

// Add this function to get iSCSI initiator name
func getISCSIInitiatorName() (string, error) {
	// Try to read from /etc/iscsi/initiatorname.iscsi
	content, err := os.ReadFile("/etc/iscsi/initiatorname.iscsi")
	if err != nil {
		return "", fmt.Errorf("failed to read initiator name: %w", err)
	}

	// Parse the content to get InitiatorName
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "InitiatorName=") {
			// Get the full initiator name
			fullName := strings.TrimPrefix(line, "InitiatorName=")
			// Split by colon and get the last part
			parts := strings.Split(fullName, ":")
			if len(parts) > 1 {
				return strings.TrimSpace(parts[len(parts)-1]), nil
			}
			return "", fmt.Errorf("invalid initiator name format")
		}
	}

	return "", fmt.Errorf("initiator name not found in config file")
}

// Add this function to get system serial number
func getSystemSerialNumber() (string, error) {
	// Try to read from DMI
	content, err := os.ReadFile("/sys/class/dmi/id/product_serial")
	if err != nil {
		// Try alternative command if file doesn't exist
		cmd := exec.Command("dmidecode", "-s", "system-serial-number")
		output, cmdErr := cmd.Output()
		if cmdErr != nil {
			return "", fmt.Errorf("failed to get system serial number: %w", err)
		}
		return strings.TrimSpace(string(output)), nil
	}

	return strings.TrimSpace(string(content)), nil
}
