package utils

import (
	"fmt"
	"log"
	"os"
	"strings"
)

var HOSTNAME string

func Setup() {
	GetHostName()
	initMap()
}

func GetHostName() {
	filePath := "/etc/host_name"
	log.Println("host_name filePath:", filePath)
	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		log.Println("无法读取文件:", err)
		return
	}
	outputString := strings.ReplaceAll(string(content), "\n", "")
	HOSTNAME = outputString
	log.Println("HOSTNAME:", outputString)
	fmt.Println("init HOSTNAME Finish!")
}
