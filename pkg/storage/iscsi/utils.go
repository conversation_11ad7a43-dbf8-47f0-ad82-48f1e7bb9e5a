package iscsi

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/dell/goiscsi"
)

type IscsiDevice struct {
	DevicePath    string   `json:"device_path"`
	Mounted_nodes []string `json:"mounted_nodes"`
	Mounted_path  []string `json:"mounted_path"`
	FsType        string   `json:"fs_type"`
	SCSIAddress   string   `json:"scsi_address"`
	Vendor        string   `json:"vendor"`
	Model         string   `json:"model"`
	Revision      string   `json:"revision"`
	State         string   `json:"state"`
	HostNumber    string   `json:"host_number"`
	Channel       string   `json:"channel"`
	Id            string   `json:"id"`
	Lun           string   `json:"lun"`
}

type IscsiTargetStatus struct {
	TargetIQN   string        `json:"target_iqn"`
	Portal      string        `json:"portal"`
	Connected   bool          `json:"connected"`
	Devices     []IscsiDevice `json:"devices"`
	LastError   string        `json:"last_error,omitempty"`
	LastChecked time.Time     `json:"last_checked"`
}

// Target represents a simplified iSCSI target structure
type Target struct {
	TargetIQN string `json:"target_iqn"`
}

// TargetList represents a list of iSCSI targets
type TargetList struct {
	Targets []Target `json:"targets"`
}

func getTargetsAsJSON(address string) (TargetList, error) {
	// var c goiscsi.ISCSIinterface
	c := goiscsi.NewLinuxISCSI(map[string]string{})

	targets, err := c.DiscoverTargets(address, false)
	if err != nil {
		return TargetList{}, fmt.Errorf("发现目标失败: %v", err)
	}

	// 创建目标列表结构
	targetList := TargetList{
		Targets: make([]Target, 0, len(targets)),
	}

	// 添加每个目标到列表
	for _, t := range targets {
		targetList.Targets = append(targetList.Targets, Target{
			TargetIQN: t.Target,
		})
	}

	return targetList, nil
}

// 从映射转换为设备结构体
func mapToIscsiDevice(deviceMap map[string]string) IscsiDevice {
	device := IscsiDevice{
		DevicePath:  deviceMap["Device Path"],
		SCSIAddress: deviceMap["SCSI Address"],
		Vendor:      deviceMap["Vendor"],
		Model:       deviceMap["Model"],
		Revision:    deviceMap["Rev"],
		State:       deviceMap["Disk State"],
		HostNumber:  deviceMap["Host Number"],
		Channel:     deviceMap["Channel"],
		Id:          deviceMap["Id"],
		Lun:         deviceMap["Lun"],
	}
	return device
}

// 获取iSCSI目标状态
func getISCSITargetStatus(sessionID string, details map[string]string) (IscsiTargetStatus, error) {

	//fmt.Println("iqn:", details["targetname"])

	status := IscsiTargetStatus{
		TargetIQN:   details["targetname"],
		LastChecked: time.Now(),
		Connected:   false,
	}

	// 尝试获取门户信息
	portalKey := "targetaddress" // 可能因系统而异
	if portal, ok := details[portalKey]; ok {
		status.Portal = portal
	} else if portal, ok := details["targetip"]; ok {
		port := "3260" // 默认端口
		if portVal, ok := details["targetport"]; ok {
			port = portVal
		}
		status.Portal = fmt.Sprintf("%s:%s", portal, port)
	}

	// 获取连接状态
	if state, ok := details["state"]; ok {
		status.Connected = (state == "running" || state == "LOGGED_IN")
	}

	// 获取并转换设备信息
	deviceMaps, err := getISCSIAttachedDevices(sessionID)
	if err != nil {
		status.LastError = err.Error()
		return status, err
	}

	// 将设备映射转换为结构体
	for _, deviceMap := range deviceMaps {
		// 检查是否有磁盘信息
		if _, ok := deviceMap["Attached scsi disk"]; ok {
			device := mapToIscsiDevice(deviceMap)

			// 增加设备的挂载节点
			mounted_nodes, err := getClusterNodes(device.DevicePath)
			if err == nil {
				device.Mounted_nodes = mounted_nodes
			}

			// 增加设备的挂载路径
			mounted_path, err := GetMountPathFromProcMounts(device.DevicePath)
			if err == nil {
				device.Mounted_path = mounted_path
			}

			// 增加设备的文件系统类型
			fsType, err := getFSTypeUsingBlkid(device.DevicePath)
			if err == nil {
				device.FsType = fsType
			}

			status.Devices = append(status.Devices, device)
		}
	}

	return status, nil
}

// 修改返回类型为设备列表
func getISCSIAttachedDevices(sessionID string) ([]map[string]string, error) {
	var devices []map[string]string
	sessionPath := filepath.Join("/sys/class/iscsi_session", sessionID)

	// 获取 device 目录
	devicePath := filepath.Join(sessionPath, "device")

	// 查找 target 目录，获取 host 编号
	targetDirs, err := filepath.Glob(filepath.Join(devicePath, "target*"))
	if err != nil || len(targetDirs) == 0 {
		return nil, fmt.Errorf("无法找到 target 目录: %v", err)
	}

	// 从 target 路径中提取 host 编号
	targetDir := filepath.Base(targetDirs[0])
	hostNumRegex := regexp.MustCompile(`target(\d+):`)
	hostMatches := hostNumRegex.FindStringSubmatch(targetDir)
	if len(hostMatches) < 2 {
		return nil, fmt.Errorf("无法提取 host 编号: %s", targetDir)
	}

	hostNum := hostMatches[1]

	// 获取主机通用信息
	hostInfo := make(map[string]string)
	hostInfo["Host Number"] = hostNum

	// 获取状态
	stateFile := filepath.Join(devicePath, "state")
	stateBytes, err := ioutil.ReadFile(stateFile)
	if err == nil {
		hostInfo["State"] = strings.TrimSpace(string(stateBytes))
	} else {
		hostInfo["State"] = "unknown"
	}

	// 获取所有的 LUN
	lunPaths, err := filepath.Glob(filepath.Join(targetDirs[0], "*"))
	if err != nil || len(lunPaths) == 0 {
		// 如果没有找到 LUN，仍然返回主机信息
		devices = append(devices, hostInfo)
		return devices, nil
	}

	// 处理每个 LUN
	for _, lunPath := range lunPaths {
		lunDir := filepath.Base(lunPath)

		// 验证这是一个 LUN 目录 (格式为 H:C:T:L)
		lunParts := strings.Split(lunDir, ":")
		if len(lunParts) != 4 {
			continue
		}

		// 为每个 LUN 创建一个新的设备信息 map，包含主机信息
		deviceInfo := make(map[string]string)
		for k, v := range hostInfo {
			deviceInfo[k] = v
		}

		channelID := lunParts[1]
		targetID := lunParts[2]
		lun := lunParts[3]

		deviceInfo["Channel"] = channelID
		deviceInfo["Id"] = targetID
		deviceInfo["Lun"] = lun
		deviceInfo["SCSI Address"] = fmt.Sprintf("%s:%s:%s:%s", hostNum, channelID, targetID, lun)

		// 获取 LUN 状态
		lunStateFile := filepath.Join(lunPath, "state")
		lunStateBytes, err := ioutil.ReadFile(lunStateFile)
		if err == nil {
			deviceInfo["LUN State"] = strings.TrimSpace(string(lunStateBytes))
		}

		// 获取块设备
		blockPath := filepath.Join(lunPath, "block")
		if _, err := os.Stat(blockPath); err == nil {
			blockDevices, err := ioutil.ReadDir(blockPath)
			if err == nil && len(blockDevices) > 0 {
				diskName := blockDevices[0].Name()
				deviceInfo["Attached scsi disk"] = diskName
				deviceInfo["Device Path"] = "/dev/" + diskName

				// 获取磁盘状态
				diskStatePath := filepath.Join("/sys/block", diskName, "device/state")
				diskStateBytes, err := ioutil.ReadFile(diskStatePath)
				if err == nil {
					deviceInfo["Disk State"] = strings.TrimSpace(string(diskStateBytes))
				}

				// 获取厂商、型号和版本信息
				vendorPath := filepath.Join("/sys/block", diskName, "device/vendor")
				modelPath := filepath.Join("/sys/block", diskName, "device/model")
				revPath := filepath.Join("/sys/block", diskName, "device/rev")

				if vendorBytes, err := ioutil.ReadFile(vendorPath); err == nil {
					deviceInfo["Vendor"] = strings.TrimSpace(string(vendorBytes))
				}
				if modelBytes, err := ioutil.ReadFile(modelPath); err == nil {
					deviceInfo["Model"] = strings.TrimSpace(string(modelBytes))
				}
				if revBytes, err := ioutil.ReadFile(revPath); err == nil {
					deviceInfo["Rev"] = strings.TrimSpace(string(revBytes))
				}
			}
		}

		// 将此设备添加到设备列表
		devices = append(devices, deviceInfo)
	}

	return devices, nil
}

func getISCSISessionInfo() (map[string]map[string]string, error) {
	sessions := make(map[string]map[string]string)

	// Path to iSCSI sessions
	sessionPath := "/sys/class/iscsi_session"

	// Get all session directories
	dirs, err := ioutil.ReadDir(sessionPath)
	if err != nil {
		return nil, err
	}

	for _, dir := range dirs {
		sessionID := dir.Name()
		sessions[sessionID] = make(map[string]string)

		// Get session attributes
		attrPath := filepath.Join(sessionPath, sessionID, "device")
		attrFiles, _ := ioutil.ReadDir(attrPath)

		for _, file := range attrFiles {
			if file.IsDir() {
				continue
			}

			filePath := filepath.Join(attrPath, file.Name())
			content, err := ioutil.ReadFile(filePath)
			if err != nil {
				continue
			}

			sessions[sessionID][file.Name()] = strings.TrimSpace(string(content))
		}

		// Get target name
		targetNamePath := filepath.Join(sessionPath, sessionID, "targetname")
		targetName, err := ioutil.ReadFile(targetNamePath)
		if err == nil {
			sessions[sessionID]["targetname"] = strings.TrimSpace(string(targetName))
		}
	}

	return sessions, nil
}

func getClusterNodes(devicePath string) ([]string, error) {
	cmd := exec.Command("mounted.ocfs2", "-f")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("执行mounted.ocfs2命令失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("unexpected output format")
	}

	// 解析标题行，确定每列的起始位置
	headerLine := lines[0]
	columnPositions := make(map[string]int)

	// 确定各列的起始位置
	columnPositions["Device"] = 0
	columnPositions["Stack"] = strings.Index(headerLine, "Stack")
	columnPositions["Cluster"] = strings.Index(headerLine, "Cluster")
	columnPositions["F"] = strings.Index(headerLine, "F")
	columnPositions["Nodes"] = strings.Index(headerLine, "Nodes")

	// 查找目标设备
	for i := 1; i < len(lines); i++ {
		line := lines[i]
		if line == "" {
			continue
		}

		// 检查设备名是否匹配
		deviceField := strings.TrimSpace(line[:columnPositions["Stack"]])
		if deviceField == devicePath {
			// 提取节点字段
			nodesField := ""
			if columnPositions["Nodes"] < len(line) {
				nodesField = strings.TrimSpace(line[columnPositions["Nodes"]:])
			}

			// 处理"Not mounted"情况
			if nodesField == "Not mounted" || nodesField == "" {
				return []string{}, nil
			}

			// 处理数字情况
			if _, err := strconv.Atoi(nodesField); err == nil {
				return []string{nodesField}, nil
			}

			// 处理逗号分隔的节点列表
			if strings.Contains(nodesField, ",") {
				nodes := []string{}
				for _, node := range strings.Split(nodesField, ",") {
					node = strings.TrimSpace(node)
					if node != "" {
						nodes = append(nodes, node)
					}
				}
				return nodes, nil
			}

			// 单个节点名称
			return []string{nodesField}, nil
		}
	}

	return nil, fmt.Errorf("未找到设备 %s 的信息", devicePath)
}

func GetMountPathFromProcMounts(device string) ([]string, error) {
	file, err := os.Open("/proc/mounts")
	if err != nil {
		return nil, fmt.Errorf("无法打开/proc/mounts: %v", err)
	}
	defer file.Close()

	var mountPoints []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) >= 2 && fields[0] == device {
			mountPoints = append(mountPoints, fields[1])
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取/proc/mounts时出错: %v", err)
	}

	if len(mountPoints) == 0 {
		return nil, fmt.Errorf("设备 %s 没有在/proc/mounts中找到", device)
	}

	return mountPoints, nil
}

// 使用 blkid 命令获取文件系统类型（最可靠的方法）
func getFSTypeUsingBlkid(device string) (string, error) {
	cmd := exec.Command("blkid", "-o", "value", "-s", "TYPE", device)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行 blkid 命令失败: %v", err)
	}

	fsType := strings.TrimSpace(string(output))
	return fsType, nil
}

func LoginAndGetDevices(address string, targetIQN string) ([]IscsiTargetStatus, error) {
	var c goiscsi.ISCSIinterface
	// 1. 创建 iSCSI 客户端
	c = goiscsi.NewLinuxISCSI(map[string]string{})
	// 2. 发现目标
	targets, err := c.DiscoverTargets(address, false)
	if err != nil {
		return nil, fmt.Errorf("无法发现目标: %v", err)
	}

	// 3. 查找指定的目标
	var targetToLogin goiscsi.ISCSITarget
	found := false

	for _, t := range targets {
		if t.Target == targetIQN {
			targetToLogin = t
			found = true
			break
		}
	}

	if !found {
		return nil, fmt.Errorf("找不到目标: %s", targetIQN)
	}

	// 4. 登录到目标
	err = c.PerformLogin(targetToLogin)
	if err != nil {
		return nil, fmt.Errorf("登录失败: %v", err)
	}

	fmt.Printf("成功登录到目标: %s\n", targetIQN)

	// 5. 触发系统重新扫描
	// err = c.PerformRescan()
	// if err != nil {
	// 	return nil, fmt.Errorf("重新扫描失败: %v", err)
	// }

	// // 6. 获取设备名称
	// devices, err := GetDevicesForTarget(targetIQN)
	// if err != nil {
	// 	// 如果第一种方法失败，尝试第二种方法
	// 	devices, err = GetDevicesFromByPath(targetIQN)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("无法获取设备: %v", err)
	// 	}
	// }

	return getSessionsInfo(targetIQN)

	// return devices, nil
}

// 使用 iscsiadm 命令获取设备
func GetDevicesForTarget(targetIQN string) ([]string, error) {
	// 使用 iscsiadm 命令获取会话信息
	cmd := exec.Command("iscsiadm", "-m", "session", "-P", "3")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("执行 iscsiadm 命令失败: %v", err)
	}

	// 解析输出查找设备
	return parseIscsiadmOutput(string(output), targetIQN)
}

// 解析 iscsiadm 输出获取设备名
func parseIscsiadmOutput(output string, targetIQN string) ([]string, error) {
	devices := []string{}

	// 分割为会话部分
	sessions := strings.Split(output, "Target: ")

	for _, session := range sessions {
		// 忽略空会话
		if session == "" {
			continue
		}

		// 检查此会话是否匹配目标 IQN
		if strings.HasPrefix(session, targetIQN) {
			// 使用正则表达式查找设备名称
			re := regexp.MustCompile(`Attached scsi disk (\w+)`)
			matches := re.FindAllStringSubmatch(session, -1)

			for _, match := range matches {
				if len(match) > 1 {
					devices = append(devices, "/dev/"+match[1])
				}
			}
		}
	}

	if len(devices) == 0 {
		return nil, fmt.Errorf("未找到与目标 %s 关联的设备", targetIQN)
	}

	return devices, nil
}

// 通过 /dev/disk/by-path/ 目录查找设备
func GetDevicesFromByPath(targetIQN string) ([]string, error) {
	// 执行 ls -l 命令查看 by-path 目录
	cmd := exec.Command("ls", "-l", "/dev/disk/by-path/")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("无法列出 /dev/disk/by-path/ 目录: %v", err)
	}

	// 正则表达式匹配 IQN 相关的设备链接
	// 注意：IQN 在路径中可能会被修改，所以我们使用部分匹配
	iqnParts := strings.Split(targetIQN, ":")
	if len(iqnParts) < 2 {
		return nil, fmt.Errorf("无效的 IQN 格式: %s", targetIQN)
	}

	// 提取 IQN 的唯一部分用于匹配
	iqnUniquePart := iqnParts[len(iqnParts)-1]

	// 匹配设备链接
	re := regexp.MustCompile(`ip-.*iscsi-.*` + regexp.QuoteMeta(iqnUniquePart) + `.*-lun-\d+ -> \.\.\/\.\.\/(sd\w+)`)
	matches := re.FindAllStringSubmatch(string(output), -1)

	devices := []string{}
	for _, match := range matches {
		if len(match) > 1 {
			devices = append(devices, "/dev/"+match[1])
		}
	}

	if len(devices) == 0 {
		return nil, fmt.Errorf("未找到与目标 %s 关联的设备", targetIQN)
	}

	return devices, nil
}

func getNodes() ([]goiscsi.ISCSINode, error) {
	iscsiClient := goiscsi.NewLinuxISCSI(map[string]string{})
	return iscsiClient.GetNodes()
}

func logout(target string) error {

	var goiscsiTarget goiscsi.ISCSITarget
	goiscsiTarget.Target = target

	iscsiClient := goiscsi.NewLinuxISCSI(map[string]string{})
	nodes, _ := iscsiClient.GetNodes()
	for _, node := range nodes {
		if node.Target == target {
			goiscsiTarget.Portal = node.Portal
		}
	}
	return iscsiClient.PerformLogout(goiscsiTarget)
}

func deleteNode(target string) error {
	var goiscsiTarget goiscsi.ISCSITarget
	goiscsiTarget.Target = target

	iscsiClient := goiscsi.NewLinuxISCSI(map[string]string{})
	nodes, _ := iscsiClient.GetNodes()
	for _, node := range nodes {
		if node.Target == target {
			goiscsiTarget.Portal = node.Portal
		}
	}
	return iscsiClient.DeleteNode(goiscsiTarget)
}

// func getInterface() ([]goiscsi.ISCSIinterface, error){
// 	iscsiClient := goiscsi.NewLinuxISCSI(map[string]string{})
// 	return iscsiClient.GetInterfaces()
// }

func getSessions() ([]goiscsi.ISCSISession, error) {
	iscsiClient := goiscsi.NewLinuxISCSI(map[string]string{})
	return iscsiClient.GetSessions()
}

func getSessionsInfo(target_name string) ([]IscsiTargetStatus, error) {
	// 1. 首先获取所有 iSCSI 会话信息
	ret := make([]IscsiTargetStatus, 0)
	sessions, err := getISCSISessionInfo()
	if err != nil {
		fmt.Printf("获取会话信息失败: %v\n", err)
		return ret, err
	}

	// 2. 遍历每个会话并获取其状态
	for sessionID, details := range sessions {
		if target_name != "" && target_name != details["targetname"] {
			continue
		}
		status, err := getISCSITargetStatus(sessionID, details)
		if err != nil {
			fmt.Printf("获取目标状态失败: %v\n", err)
			continue
		}

		ret = append(ret, status)

	}

	return ret, nil
}
