package iscsi

import (
	"fmt"

	"github.com/dell/goiscsi"
)

// 发现iscsi目标
func DiscoverTargets(targetIP string) (TargetList, error) {
	fmt.Println("DiscoverTargets:", targetIP)
	return getTargetsAsJSON(targetIP)
}

// 登录指定iscsi目标
func LoginTargets(address string, targetIQN string) ([]IscsiTargetStatus, error) {
	return LoginAndGetDevices(address, targetIQN)

}


// 获取目标节点
func GetNodes() ([]goiscsi.ISCSINode, error) {
	return getNodes()
}


func Logout(target string) error {
	return logout(target)
}

func DeleteNode(target string) error {
	return deleteNode(target)
}

func GetSessions() ([]goiscsi.ISCSISession, error) {
	return getSessions()
}

func GetDevicesByTarget(iqn string) ([]string, error){
	return GetDevicesForTarget(iqn)
}

func GetSessionInfo(target_name string)([]IscsiTargetStatus, error){
	return getSessionsInfo(target_name)
}