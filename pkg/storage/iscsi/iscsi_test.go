package iscsi

import (
	"fmt"
	"testing"
)

func TestDiscoverTargets_Integration(t *testing.T) {
	if testing.Short() {
		t.<PERSON>("skipping integration test")
	}

	targetIP := "************" // Use appropriate test IP
	got, err := DiscoverTargets(targetIP)
	if err != nil {
		t.<PERSON>("DiscoverTargets() failed with local IP: %v", err)
	}

	for _, target := range got.Targets {
		fmt.Println(target.TargetIQN)
	}
}
