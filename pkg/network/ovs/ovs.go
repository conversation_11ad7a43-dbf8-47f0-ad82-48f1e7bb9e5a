package ovs

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"github.com/ovn-kubernetes/libovsdb/client"
	"github.com/ovn-kubernetes/libovsdb/model"
	"github.com/ovn-kubernetes/libovsdb/ovsdb"
)

// 自定义 Bridge 表结构
type Bridge struct {
	UUID   string            `ovsdb:"_uuid"`
	Name   string            `ovsdb:"name"`
	Ports  []string          `ovsdb:"ports"`
	Config map[string]string `ovsdb:"other_config"`
}

// 自定义 Open_vSwitch 表结构
type OpenvSwitch struct {
	UUID    string   `ovsdb:"_uuid"`
	Bridges []string `ovsdb:"bridges"`
}

type Port struct {
	UUID        string            `ovsdb:"_uuid"`        // 端口唯一标识符
	Name        string            `ovsdb:"name"`         // 端口名称
	Interfaces  []string          `ovsdb:"interfaces"`   // 关联的接口列表
	Tag         *int              `ovsdb:"tag"`          // VLAN tag（可选）
	Trunks      []int             `ovsdb:"trunks"`       // VLAN trunk 列表（可选）
	ExternalIDs map[string]string `ovsdb:"external_ids"` // 外部ID（可选）
}

type Interface struct {
	UUID        string            `ovsdb:"_uuid"`
	Name        string            `ovsdb:"name"`
	Type        string            `ovsdb:"type"`
	Options     map[string]string `ovsdb:"options"`
	ExternalIDs map[string]string `ovsdb:"external_ids"`
}

func NewInterface(name string, remote_ip string) *Interface {
	return &Interface{
		UUID: uuid.New().String(), // 生成唯一的UUID
		Name: name,
		Type: "geneve",
		Options: map[string]string{
			"remote_ip": remote_ip,
		},
		ExternalIDs: map[string]string{},
	}
}

// GetOvsConnection 获取OVS连接
func GetOvsConnection(connstr string) (client.Client, error) {

	if connstr == "" {
		connstr = "unix:/var/run/openvswitch/db.sock"
	}

	// 连接到OVSDB服务器
	// 定义数据库模型
	clientDBModel, err := model.NewClientDBModel("Open_vSwitch", map[string]model.Model{
		"Bridge":       &Bridge{},
		"Open_vSwitch": &OpenvSwitch{},
		"Port":         &Port{},
		"Interface":    &Interface{},
	})
	if err != nil {
		log.Fatal("Unable to create DB model: ", err)
	}

	// 创建OVSDB客户端
	// connstr := "unix:/var/run/openvswitch/db.sock"
	ovs, err := client.NewOVSDBClient(clientDBModel, client.WithEndpoint(connstr))
	if err != nil {
		log.Fatal("Failed to create OVSDB client: ", err)
	}

	// 连接到OVSDB服务器
	err = ovs.Connect(context.Background())
	if err != nil {
		log.Fatal("Failed to connect to OVSDB: ", err)
	}
	return ovs, nil
}

// CreateBridge 创建网桥
func CreateBridge(ovs client.Client, bridgeName string) error {

	// 创建一个新的逻辑交换机
	bridge := &Bridge{
		UUID:  uuid.New().String(), // 生成唯一的UUID
		Name:  bridgeName,
		Ports: []string{},
	}

	//获取Open_vSwitch表信息
	vSwitch, _ := GetOpenvSwitch(ovs)
	rootUUID := vSwitch.UUID

	// 插入新的逻辑交换机
	insertOps, err := ovs.Create(bridge)
	if err != nil {
		return fmt.Errorf("failed to create bridge: %v", err)
	}
	ovsRow := OpenvSwitch{
		UUID: rootUUID,
	}

	// 生成将新交换机添加到 Open_vSwitch 表的 Bridges 字段的突变操作
	mutateOps, err := ovs.Where(&ovsRow).Mutate(&ovsRow, model.Mutation{
		Field:   &ovsRow.Bridges,
		Mutator: ovsdb.MutateOperationInsert,
		Value:   []string{bridge.UUID},
	})
	if err != nil {
		return fmt.Errorf("failed to create mutate operation: %v", err)
	}

	// 合并插入和突变操作
	operations := append(insertOps, mutateOps...)

	// 执行事务
	ctx := context.Background()
	resp, err := ovs.Transact(ctx, operations...)
	if err != nil {
		return fmt.Errorf("transaction failed: %v", err)
	}

	// 检查响应是否成功
	if _, err := ovsdb.CheckOperationResults(resp, operations); err != nil {
		return fmt.Errorf("transaction returned an error: %v", err)
	}

	log.Printf("Successfully created logical switch: %s", bridge.Name)
	return nil
}

func DeleteBridge(ovs client.Client, bridge *Bridge) error {

	ctx := context.Background()
	vSwitch, _ := GetOpenvSwitch(ovs)
	rootUUID := vSwitch.UUID

	deleteOp, err := ovs.Where(bridge).Delete()
	if err != nil {
		log.Fatal(err)
	}
	ovsRow := OpenvSwitch{
		UUID: rootUUID,
	}
	mutateOp, err := ovs.Where(&ovsRow).Mutate(&ovsRow, model.Mutation{
		Field:   &ovsRow.Bridges,
		Mutator: ovsdb.MutateOperationDelete,
		Value:   []string{bridge.UUID},
	})
	if err != nil {
		log.Fatal(err)
	}
	operations := append(deleteOp, mutateOp...)
	reply, err := ovs.Transact(ctx, operations...)
	if err != nil {
		return err
	}
	if _, err := ovsdb.CheckOperationResults(reply, operations); err != nil {
		return err
	}
	return nil
}

// CreatePort 创建端口
// 创建端口必须要创建Interface
func CreatePort(ovs client.Client, portName string, bridge *Bridge) error {

	// 创建一个新的 Interface
	iface := NewInterface(portName, "192.168.213.31")

	// 创建一个新的端口
	port := &Port{
		UUID:        uuid.New().String(), // 生成唯一的UUID
		Name:        portName,
		Interfaces:  []string{iface.UUID},    // 初始化接口列表
		ExternalIDs: make(map[string]string), // 初始化外部ID
	}

	// 插入新的 Interface
	insertIfaceOps, err := ovs.Create(iface)
	if err != nil {
		return fmt.Errorf("failed to create interface: %v", err)
	}

	// 插入新的端口
	insertPortOps, err := ovs.Create(port)
	if err != nil {
		return fmt.Errorf("failed to create port: %v", err)
	}

	// 生成将新端口添加到 Bridge 表的 Ports 字段的突变操作
	mutateOps, err := ovs.Where(bridge).Mutate(bridge, model.Mutation{
		Field:   &bridge.Ports,
		Mutator: ovsdb.MutateOperationInsert,
		Value:   []string{port.UUID},
	})
	if err != nil {
		return fmt.Errorf("failed to create mutate operation: %v", err)
	}

	// 合并插入和突变操作
	operations := append(insertIfaceOps, insertPortOps...)
	operations = append(operations, mutateOps...)

	// 执行事务
	ctx := context.Background()
	resp, err := ovs.Transact(ctx, operations...)
	if err != nil {
		return fmt.Errorf("transaction failed: %v", err)
	}

	// 检查响应是否成功
	if _, err := ovsdb.CheckOperationResults(resp, operations); err != nil {
		return fmt.Errorf("transaction returned an error: %v", err)
	}

	log.Printf("Successfully created port: %s", port.Name)
	return nil
}

// DeletePort 删除端口
func DeletePort(ovs client.Client, port *Port, bridge *Bridge) error {
	ctx := context.Background()

	// 生成从 Bridge 表的 Ports 字段中移除端口的突变操作
	mutateOps, err := ovs.Where(bridge).Mutate(bridge, model.Mutation{
		Field:   &bridge.Ports,
		Mutator: ovsdb.MutateOperationDelete,
		Value:   []string{port.UUID},
	})
	if err != nil {
		return fmt.Errorf("failed to create mutate operation: %v", err)
	}

	// 生成删除端口的操作
	deleteOps, err := ovs.Where(port).Delete()
	if err != nil {
		return fmt.Errorf("failed to create delete operation: %v", err)
	}

	// 合并删除和突变操作
	operations := append(deleteOps, mutateOps...)

	// 执行事务
	reply, err := ovs.Transact(ctx, operations...)
	if err != nil {
		return fmt.Errorf("transaction failed: %v", err)
	}

	// 检查响应是否成功
	if _, err := ovsdb.CheckOperationResults(reply, operations); err != nil {
		return fmt.Errorf("transaction returned an error: %v", err)
	}

	log.Printf("Successfully deleted port: %s", port.Name)
	return nil
}

// GetOpenvSwitch 查询Open_vSwitch表信息
func GetOpenvSwitch(ovs client.Client) (*OpenvSwitch, error) {
	// 查询Open_vSwitch表信息
	var ovsRows []OpenvSwitch
	err := ovs.List(context.Background(), &ovsRows)
	if err != nil {
		log.Fatal("Failed to list Open_vSwitch rows: ", err)
	}

	// 打印Open_vSwitch表信息
	return &ovsRows[0], nil
}

// GetBridges 查询所有Bridge信息
func GetBridges(ovs client.Client) ([]Bridge, error) {
	// 查询所有Bridge信息
	var bridges []Bridge
	err := ovs.List(context.Background(), &bridges)
	if err != nil {
		log.Fatal("Failed to list bridges: ", err)
	}
	return bridges, nil
}

// GetBridge 根据名称查询Bridge信息
func GetBridge(ovs client.Client, Name string) (*Bridge, error) {
	// 查询所有Bridge信息
	bridges, err := GetBridges(ovs)
	if err != nil {
		log.Println("Failed to list bridges: ", err)
		return nil, err
	}

	// 打印查询结果
	fmt.Println("Current list of bridges:")
	for _, bridge := range bridges {
		if bridge.Name == Name {
			return &bridge, nil
		}
	}
	return nil, fmt.Errorf("Bridge %s not found", Name)
}

// GetPorts 查询所有端口信息
func GetPorts(ovs client.Client) ([]Port, error) {
	// 查询所有端口信息
	var ports []Port
	err := ovs.List(context.Background(), &ports)
	if err != nil {
		log.Fatal("Failed to list ports: ", err)
	}
	return ports, nil
}

// GetPortsByBridge 根据Bridge查询所有端口信息,如果指定的portName不为空,则返回的数组只包含指定名称的端口
func GetPortsByBridge(ovs client.Client, bridge Bridge) ([]Port, error) {
	// 查询所有端口信息
	var ports []Port
	var port Port
	for _, portUUID := range bridge.Ports {
		port.UUID = portUUID
		err := ovs.Get(context.Background(), &port)
		if err != nil {
			log.Println("Failed to get port: ", err)
		}

		ports = append(ports, port)
	}
	return ports, nil
}

// 查询指定端口
func GetPortByName(ovs client.Client, bridge Bridge, portName string) (*Port, error) {
	ports, err := GetPortsByBridge(ovs, bridge)
	if err != nil {
		return nil, err
	}
	for _, port := range ports {
		if port.Name == portName {
			return &port, nil
		}
	}
	return nil, fmt.Errorf("port with name '%s' not found", portName)
}
