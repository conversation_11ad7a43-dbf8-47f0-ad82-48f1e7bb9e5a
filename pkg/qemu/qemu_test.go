package qemu

import (
	"os"
	"testing"
)

func TestCreateDiskImage(t *testing.T) {
	// 创建一个临时文件路径
	imagePath := "base_disk.qcow2"
	// defer os.Remove(imagePath) // 测试结束后删除临时文件

	// 测试创建磁盘镜像
	err := CreateDiskImage(imagePath, "1G")
	if err != nil {
		t.Fatalf("Failed to create disk image: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		t.Fatalf("Disk image file was not created: %v", err)
	}
}

func TestCreateLinkedClone(t *testing.T) {
	// 创建基础磁盘镜像
	baseImagePath := "/var/lib/libvirt/images/ubuntu24.04.qcow2"
	// defer os.Remove(baseImagePath) // 测试结束后删除临时文件
	err := CreateDiskImage(baseImagePath, "1G")
	if err != nil {
		t.Fatalf("Failed to create base disk image: %v", err)
	}

	// 创建链接克隆镜像
	cloneImagePath := "clone_disk.qcow2"
	// defer os.Remove(cloneImagePath) // 测试结束后删除临时文件
	err = CreateLinkedClone(baseImagePath, cloneImagePath)
	if err != nil {
		t.Fatalf("Failed to create linked clone: %v", err)
	}

	// 检查克隆文件是否存在
	if _, err := os.Stat(cloneImagePath); os.IsNotExist(err) {
		t.Fatalf("Linked clone file was not created: %v", err)
	}
}