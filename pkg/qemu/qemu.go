package qemu

import (
	"log"
	"os/exec"
)

// 创建一个新的QCOW2格式的磁盘镜像
func CreateDiskImage(imagePath string, imageSize string) error {
	cmd := exec.Command("qemu-img", "create", "-f", "qcow2", imagePath, imageSize)
	if err := cmd.Run(); err != nil {
		return err
	}
	log.Printf("Disk image created: %s\n", imagePath)
	return nil
}

// 基于已有的磁盘镜像进行链接克隆，创建增量镜像
func CreateLinkedClone(baseImagePath string, cloneImagePath string) error {
	// 明确指定基础镜像的格式为 qcow2
	cmd := exec.Command("qemu-img", "create", "-f", "qcow2", "-b", baseImagePath, "-F", "qcow2", cloneImagePath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Command failed: %s\nOutput: %s\n", cmd.String(), string(output))
		return err
	}
	log.Printf("Linked clone created: %s based on %s\nOutput: %s\n", cloneImagePath, baseImagePath, string(output))
	return nil
}