package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
			 // 设置允许的来源，这里设置为 '*' 允许所有来源，生产环境中应具体指定
			 c.Header("Access-Control-Allow-Origin", "*")
        
			 // 设置允许的HTTP方法
			 c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			 
			 // 设置允许的HTTP头部
			 c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With")

			 // 如果是预检请求(OPTIONS)，直接返回200
			 if c.Request.Method == "OPTIONS" {
					 c.AbortWithStatus(http.StatusOK)
					 return
			 }

			c.Next()
	}
}