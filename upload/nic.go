package upload

import (
	"context"
	"encoding/json"
	"fmt"
	"hci_agent/pkg/network/ovs"
	"net"
	"net/http"
	"os"
	"os/exec"
	"reflect"
	"strings"

	"github.com/jaypipes/ghw"
	gopsnet "github.com/shirou/gopsutil/v3/net"
)

// 检查是否为物理网卡
func isPhysicalInterface(iface net.Interface) bool {
	// 检查硬件地址是否存在且不为空，并且不为回环接口
	if iface.Flags&net.FlagLoopback == 0 && iface.HardwareAddr != nil && len(iface.HardwareAddr) > 0 {
		// 检查是否存在 /sys/class/net/<interface>/device 文件夹
		if _, err := os.Stat("/sys/class/net/" + iface.Name + "/device"); os.IsNotExist(err) {
			return false
		}
		return true
	}
	return false
}

// 判断gopsutil网卡是否为物理网卡
func isPhysicalInterfaceGopsutil(ifaceName string, flags []string, hardwareAddr string) bool {
	isLoopback := false
	for _, flag := range flags {
		if flag == "loopback" {
			isLoopback = true
			break
		}
	}
	if !isLoopback && hardwareAddr != "" {
		if _, err := os.Stat("/sys/class/net/" + ifaceName + "/device"); err == nil {
			return true
		}
	}
	return false
}

// 判断网卡是否被网桥(master)使用，并返回网桥名
func getBridgeMaster(ifaceName string) (string, bool) {
	masterPath := "/sys/class/net/" + ifaceName + "/master"
	link, err := os.Readlink(masterPath)
	if err != nil {
		return "", false
	}
	parts := strings.Split(link, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1], true
	}
	return "", false
}

// 获取 OVS 物理网卡到 bridge 的映射
func getOvsNicBridgeMap() map[string]string {
	nicToBridge := make(map[string]string)
	fmt.Println("[OVS] Connecting to OVSDB...")
	client, err := ovs.GetOvsConnection("")
	if err != nil {
		fmt.Println("[OVS] Connection error:", err)
		return nicToBridge
	}
	client.MonitorAll(context.TODO())
	defer client.Disconnect()
	bridges, err := ovs.GetBridges(client)
	if err != nil {
		fmt.Println("[OVS] GetBridges error:", err)
		return nicToBridge
	}
	fmt.Printf("[OVS] Found %d bridges\n", len(bridges))
	for _, bridge := range bridges {
		fmt.Printf("[OVS] Bridge: %s, Ports: %v\n", bridge.Name, bridge.Ports)
		ports, _ := ovs.GetPortsByBridge(client, bridge)
		fmt.Printf("[OVS] Bridge %s ports detail: %v\n", bridge.Name, ports)
		for _, port := range ports {
			if port.Name != "" {
				fmt.Printf("[OVS] Mapping port %s -> bridge %s\n", port.Name, bridge.Name)
				nicToBridge[port.Name] = bridge.Name
			}
		}
	}
	fmt.Println("[OVS] Final nicToBridge map:", nicToBridge)
	return nicToBridge
}

// 网卡详细信息结构体
// 可根据需要扩展
// 注意：部分字段可能因权限或环境缺失
// OVS桥信息只查找OVS类型

type NicDetail struct {
	Name       string   `json:"name"`
	MAC        string   `json:"mac"`
	MTU        int      `json:"mtu"`
	Status     string   `json:"status"`
	LinkStatus string   `json:"link_status"`
	Speed      string   `json:"speed"`
	Duplex     string   `json:"duplex"`
	IPv4s      []string `json:"ipv4s"`
	IPv6s      []string `json:"ipv6s"`
	IsPhysical bool     `json:"is_physical"`
	IsBridge   bool     `json:"is_bridge"`
	BridgeName string   `json:"bridge_name,omitempty"`
	Vendor     string   `json:"vendor,omitempty"`
	Traffic    struct {
		BytesSent   uint64 `json:"bytes_sent"`
		BytesRecv   uint64 `json:"bytes_recv"`
		PacketsSent uint64 `json:"packets_sent"`
		PacketsRecv uint64 `json:"packets_recv"`
		Errin       uint64 `json:"errin"`
		Errout      uint64 `json:"errout"`
		Dropin      uint64 `json:"dropin"`
		Dropout     uint64 `json:"dropout"`
	} `json:"traffic"`
}

// ethtool信息
func getEthtoolInfo(ifaceName string) (speed, duplex, linkStatus string) {
	cmd := exec.Command("ethtool", ifaceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", "", "unknown"
	}
	for _, line := range strings.Split(string(output), "\n") {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Speed:") {
			speed = strings.TrimSpace(strings.TrimPrefix(line, "Speed:"))
		}
		if strings.HasPrefix(line, "Duplex:") {
			duplex = strings.TrimSpace(strings.TrimPrefix(line, "Duplex:"))
		}
		if strings.HasPrefix(line, "Link detected:") {
			if strings.HasSuffix(line, "yes") {
				linkStatus = "up"
			} else {
				linkStatus = "down"
			}
		}
	}
	return
}

// 获取所有物理网卡详细信息
func GetAllPhysicalNicDetails() ([]NicDetail, error) {
	interfaces, err := gopsnet.Interfaces()
	if err != nil {
		return nil, err
	}
	ioStats, _ := gopsnet.IOCounters(true)
	ioMap := make(map[string]gopsnet.IOCountersStat)
	for _, stat := range ioStats {
		ioMap[stat.Name] = stat
	}
	nicDetails := map[string]*ghw.NIC{}
	if ghwNet, err := ghw.Network(); err == nil {
		for _, nic := range ghwNet.NICs {
			nicDetails[nic.Name] = nic
		}
	}
	ovsNicBridge := getOvsNicBridgeMap()
	var result []NicDetail
	for _, iface := range interfaces {
		if !isPhysicalInterfaceGopsutil(iface.Name, iface.Flags, iface.HardwareAddr) {
			continue
		}
		isUp := false
		for _, flag := range iface.Flags {
			if flag == "up" {
				isUp = true
				break
			}
		}
		nd := NicDetail{
			Name:       iface.Name,
			MAC:        iface.HardwareAddr,
			MTU:        iface.MTU,
			Status:     "down",
			LinkStatus: "unknown",
			IsPhysical: true,
		}
		if isUp {
			nd.Status = "up"
		}
		// var ipv4s, ipv6s []string
		ipv4s := make([]string, 0)
		ipv6s := make([]string, 0)
		for _, addr := range iface.Addrs {
			ip := addr.Addr
			if strings.Contains(ip, ":") {
				ipv6s = append(ipv6s, ip)
			} else {
				ipv4s = append(ipv4s, ip)
			}
		}
		nd.IPv4s = ipv4s
		nd.IPv6s = ipv6s
		speed, duplex, link := getEthtoolInfo(iface.Name)
		if speed != "" {
			nd.Speed = speed
		}
		if duplex != "" {
			nd.Duplex = duplex
		}
		if link != "" {
			nd.LinkStatus = link
		}
		vendor := ""
		if nic, ok := nicDetails[iface.Name]; ok {
			nicVal := reflect.ValueOf(nic).Elem()
			vendorField := nicVal.FieldByName("Vendor")
			if vendorField.IsValid() && vendorField.Kind() == reflect.String && vendorField.String() != "" {
				vendor = vendorField.String()
			} else {
				productField := nicVal.FieldByName("Product")
				if productField.IsValid() && productField.Kind() == reflect.String && productField.String() != "" {
					vendor = productField.String()
				}
			}
		}
		nd.Vendor = vendor
		if stat, ok := ioMap[iface.Name]; ok {
			nd.Traffic.BytesSent = stat.BytesSent
			nd.Traffic.BytesRecv = stat.BytesRecv
			nd.Traffic.PacketsSent = stat.PacketsSent
			nd.Traffic.PacketsRecv = stat.PacketsRecv
			nd.Traffic.Errin = stat.Errin
			nd.Traffic.Errout = stat.Errout
			nd.Traffic.Dropin = stat.Dropin
			nd.Traffic.Dropout = stat.Dropout
		}
		if br, ok := ovsNicBridge[iface.Name]; ok {
			nd.IsBridge = true
			nd.BridgeName = br
		} else if br, ok := getBridgeMaster(iface.Name); ok {
			nd.IsBridge = true
			nd.BridgeName = br
		} else {
			nd.IsBridge = false
		}
		result = append(result, nd)
	}
	return result, nil
}

// 根据 OVS bridge 名称获取其下所有物理网卡详细信息
func GetPhysicalNicsByOvsBridge(bridgeName string) ([]NicDetail, error) {
	client, err := ovs.GetOvsConnection("")
	if err != nil {
		return nil, err
	}
	defer client.Disconnect()
	client.MonitorAll(context.TODO())
	bridge, err := ovs.GetBridge(client, bridgeName)
	if err != nil {
		return nil, nil // 按需返回空
	}
	ports, err := ovs.GetPortsByBridge(client, *bridge)
	fmt.Println("端口信息：", ports)
	if err != nil {
		return nil, nil
	}
	// 收集所有 port 下的 interface name
	var nicNames []string
	for _, port := range ports {

		nicNames = append(nicNames, port.Name)

	}
	// 获取所有物理网卡详细信息
	nicDetails, err := GetAllPhysicalNicDetails()
	if err != nil {
		return nil, err
	}
	// 过滤出属于该 bridge 的物理网卡
	result := make([]NicDetail, 0)
	for _, nd := range nicDetails {
		for _, n := range nicNames {
			if nd.Name == n {
				result = append(result, nd)
			}
		}
	}
	fmt.Println("bridgeName:", bridgeName)
	fmt.Println("nicNames from OVS:", nicNames)
	for _, nd := range nicDetails {
		fmt.Println("physical nic:", nd.Name)
	}
	return result, nil
}

// POST /nic/by_ovs_bridge {"name": "bridgeName"}
func HandlerNicByOvsBridge(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	type req struct {
		Name string `json:"name"`
	}
	var body req
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil || body.Name == "" {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	list, err := GetPhysicalNicsByOvsBridge(body.Name)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(list)
}

func HandlerNic(w http.ResponseWriter, r *http.Request) {
	// 获取所有接口
	interfaces, err := gopsnet.Interfaces()
	if err != nil {
		http.Error(w, "Unable to retrieve network interfaces", http.StatusInternalServerError)
		return
	}
	// 获取流量统计
	ioStats, _ := gopsnet.IOCounters(true)
	ioMap := make(map[string]gopsnet.IOCountersStat)
	for _, stat := range ioStats {
		ioMap[stat.Name] = stat
	}
	// 获取ghw网卡信息
	nicDetails := map[string]*ghw.NIC{}
	if ghwNet, err := ghw.Network(); err == nil {
		for _, nic := range ghwNet.NICs {
			nicDetails[nic.Name] = nic
		}
	}
	// 获取所有网桥信息
	// 查找所有网桥目录
	ifacesDir, err := os.ReadDir("/sys/class/net/")
	if err == nil {
		for _, entry := range ifacesDir {
			bridgePath := "/sys/class/net/" + entry.Name() + "/bridge"
			if stat, err := os.Stat(bridgePath); err == nil && stat.IsDir() {
				brifDir := "/sys/class/net/" + entry.Name() + "/brif/"
				brifEntries, _ := os.ReadDir(brifDir)
				_ = brifEntries // 已不再使用
			}
		}
	}
	// OVS物理网卡->bridge名映射
	ovsNicBridge := getOvsNicBridgeMap()
	fmt.Println(ovsNicBridge)
	// 获取OVS桥信息
	// 组装详细信息
	var result []NicDetail
	for _, iface := range interfaces {
		if !isPhysicalInterfaceGopsutil(iface.Name, iface.Flags, iface.HardwareAddr) {
			continue
		}
		isUp := false
		for _, flag := range iface.Flags {
			if flag == "up" {
				isUp = true
				break
			}
		}
		nd := NicDetail{
			Name:       iface.Name,
			MAC:        iface.HardwareAddr,
			MTU:        iface.MTU,
			Status:     "down",
			LinkStatus: "unknown",
			IsPhysical: true,
		}
		if isUp {
			nd.Status = "up"
		}
		// IPs分开
		// var ipv4s, ipv6s []string
		ipv4s := make([]string, 0)
		ipv6s := make([]string, 0)
		for _, addr := range iface.Addrs {
			ip := addr.Addr
			if strings.Contains(ip, ":") {
				ipv6s = append(ipv6s, ip)
			} else {
				ipv4s = append(ipv4s, ip)
			}
		}
		nd.IPv4s = ipv4s
		nd.IPv6s = ipv6s
		// ethtool
		speed, duplex, link := getEthtoolInfo(iface.Name)
		if speed != "" {
			nd.Speed = speed
		}
		if duplex != "" {
			nd.Duplex = duplex
		}
		if link != "" {
			nd.LinkStatus = link
		}
		// ghw vendor 兼容多种字段
		vendor := ""
		if nic, ok := nicDetails[iface.Name]; ok {
			nicVal := reflect.ValueOf(nic).Elem()
			vendorField := nicVal.FieldByName("Vendor")
			if vendorField.IsValid() && vendorField.Kind() == reflect.String && vendorField.String() != "" {
				vendor = vendorField.String()
			} else {
				productField := nicVal.FieldByName("Product")
				if productField.IsValid() && productField.Kind() == reflect.String && productField.String() != "" {
					vendor = productField.String()
				}
			}
		}
		nd.Vendor = vendor
		// 流量统计
		if stat, ok := ioMap[iface.Name]; ok {
			nd.Traffic.BytesSent = stat.BytesSent
			nd.Traffic.BytesRecv = stat.BytesRecv
			nd.Traffic.PacketsSent = stat.PacketsSent
			nd.Traffic.PacketsRecv = stat.PacketsRecv
			nd.Traffic.Errin = stat.Errin
			nd.Traffic.Errout = stat.Errout
			nd.Traffic.Dropin = stat.Dropin
			nd.Traffic.Dropout = stat.Dropout
		}
		// 判断是否被OVS bridge使用（优先）
		if br, ok := ovsNicBridge[iface.Name]; ok {
			nd.IsBridge = true
			nd.BridgeName = br
		} else if br, ok := getBridgeMaster(iface.Name); ok {
			nd.IsBridge = true
			nd.BridgeName = br
		} else {
			nd.IsBridge = false
		}
		// 移除网桥相关逻辑
		// 只保留物理网卡信息
		result = append(result, nd)
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}
