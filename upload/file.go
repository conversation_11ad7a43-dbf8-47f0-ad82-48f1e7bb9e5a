package upload

import (
	"encoding/json"
	"fmt"
	"hci_agent/model"
	"hci_agent/pkg/setting"
	"hci_agent/pkg/utils"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"math/rand"
	"strings"

	"github.com/google/uuid"
)

func HandlerUpload(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		// Parse the multipart form
		err := r.ParseMultipartForm(10 << 20) // 10 MB
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Get the uploaded file
		file, handler, err := r.FormFile("file")
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer file.Close()

		// Create the uploads directory if it doesn't exist
		if _, err := os.Stat("uploads"); os.IsNotExist(err) {
			os.Mkdir("uploads", 0755)
		}

		// Create the file on the server
		dst, err := os.Create(filepath.Join("uploads", handler.Filename))
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer dst.Close()

		// Copy the uploaded file to the server
		_, err = io.Copy(dst, file)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		fmt.Fprintf(w, "File uploaded successfully: %s\n", handler.Filename)
	} else {
		//w.WriteHeader(http.StatusOK)
		//fmt.Fprintln(w, "upload file ....")
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)

	}

}

func HandlerImageUpload(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		// Parse the multipart form
		err := r.ParseMultipartForm(10 << 20) // 10 MB
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Get the uploaded file
		file, handler, err := r.FormFile("file")
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer file.Close()

		// 上传文件的信息
		fileName := handler.Filename
		fileSize := handler.Size
		fileType := handler.Header.Get("Content-Type")
		pool_id := r.FormValue("pool_id")
		host := r.FormValue("host")
		println(fileName, fileSize, fileType, pool_id, host)

		// 数据库获取存储池信息
		var storage_pool model.StoragePool
		storage_pool.ID = pool_id
		storage_pool_info, err := storage_pool.GetInfoById()
		if err != nil {
			fmt.Println("存储池信息不存在：", err.Error())
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// Create the uploads directory if it doesn't exist
		if _, err := os.Stat(storage_pool_info.StorageLocalDir); os.IsNotExist(err) {
			os.Mkdir("uploads", 0755)
		}

		// Create the file on the server
		dst, err := os.Create(filepath.Join(storage_pool_info.StorageLocalDir, handler.Filename))
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer dst.Close()

		// Copy the uploaded file to the server
		_, err = io.Copy(dst, file)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 处理libvirt信息
		libvirtConn, _ := utils.GetLibvirtConn(setting.AppSetting.Host)
		err = utils.LibvirtPoolRefresh(libvirtConn, storage_pool_info.Name)
		if err != nil {
			fmt.Println("存储池刷新失败", err.Error())
		}

		volumeInfo, err := utils.LibvirtVolumeInfo(libvirtConn, storage_pool_info.Name, fileName)
		if err != nil {
			fmt.Println("存储卷信息获取失败：", err.Error())
		}

		// 获取存储卷信息，保存到数据库
		var volume model.StorageVolume
		volume.ID = uuid.New().String()
		volume.StoragePoolId = pool_id
		volume.Name = volumeInfo.Name
		volume.TypeCode = volumeInfo.Format
		volume.JoinType = "上传"
		volume.Path = storage_pool_info.StorageLocalDir
		volume.Encrypt = 0
		volume.Status = 0
		volume.Capacity = fileSize
		volume.Allocation = fileSize
		volume.Preallocation = ""
		volume.Time = time.Now()
		volume.Remark = ""
		err = volume.Create()
		if err != nil {
			fmt.Println("存储池信息添加失败：", err.Error())
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		fmt.Fprintf(w, "File uploaded successfully: %s\n", handler.Filename)
	} else {
		//w.WriteHeader(http.StatusOK)
		//fmt.Fprintln(w, "upload file ....")
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)

	}

}

func Volume_info(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		var err error

		pool_name := r.FormValue("pool_name")
		volume_name := r.FormValue("volume_name")
		libvirtConn, _ := utils.GetLibvirtConn(setting.AppSetting.Host)

		volumeInfo, err := utils.LibvirtVolumeInfo(libvirtConn, pool_name, volume_name)
		if err != nil {
			fmt.Println("存储卷信息获取失败：", err.Error())
		}

		fmt.Println(volumeInfo.Format)
		fmt.Fprintf(w, "存储卷信息: %s\n", volumeInfo.Format)
	}

}

func PoolInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {

		var p model.StoragePool
		r, err := p.Get()
		if err != nil {
			fmt.Println(err.Error())
			fmt.Fprintf(w, "存储卷信息: %v\n", err.Error())
			return
		}

		fmt.Fprintf(w, "存储卷信息: %v\n", r)
	}

}

const (
	TEMP_DIR = "./chunks" // 分片临时存储路径
)

var (
	lock     sync.Mutex
	executor = make(chan struct{}, 5) // 控制并发数量
)

func init() {
	// 确保临时目录存在
	if err := os.MkdirAll(TEMP_DIR, 0755); err != nil {
		panic(fmt.Sprintf("Failed to create temp directory: %v", err))
	}
}

func saveChunk(filename string, index int, data []byte) {
	filepath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, index))
	err := os.WriteFile(filepath, data, 0644)
	if err != nil {
		fmt.Printf("Error saving chunk: %v\n", err)
		return
	}
	fmt.Printf("Saved chunk %d of %s\n", index, filename)
}

func HandleChunkUpload(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头部
	w.Header().Set("Access-Control-Allow-Origin", "*") // 允许所有来源，或指定具体的来源
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		// 预检请求直接返回200状态码
		w.WriteHeader(http.StatusOK)
		return
	}

	// 如果不是POST请求，则返回405 Method Not Allowed
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析multipart form
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	file, _, err := r.FormFile("chunk")
	if err != nil {
		http.Error(w, "No chunk part", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// 读取表单数据
	index := r.FormValue("index")
	if index == "" {
		http.Error(w, "No index provided", http.StatusBadRequest)
		return
	}

	filename := r.FormValue("filename")
	if filename == "" {
		http.Error(w, "No filename provided", http.StatusBadRequest)
		return
	}

	// 读取文件数据
	data, err := io.ReadAll(file)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 使用channel控制并发
	executor <- struct{}{} // 获取执行许可
	go func() {
		defer func() { <-executor }() // 释放执行许可
		saveChunk(filename, atoi(index), data)
	}()

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"message": "Chunk received",
	})
}

// 生成随机字符串
func randomString(n int) string {
	letters := []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// 获取唯一文件路径，若已存在则自动重命名
func getUniqueFilePath(outputDir, filename string) string {
	ext := filepath.Ext(filename)
	name := strings.TrimSuffix(filename, ext)
	newPath := filepath.Join(outputDir, filename)
	for {
		if _, err := os.Stat(newPath); os.IsNotExist(err) {
			return newPath
		}
		// 文件已存在，生成新名字
		newFilename := fmt.Sprintf("%s-%s%s", name, randomString(6), ext)
		newPath = filepath.Join(outputDir, newFilename)
	}
}

func HandleComplete(w http.ResponseWriter, r *http.Request) {
	fmt.Println("HandleComplete")
	// 设置CORS头部
	w.Header().Set("Access-Control-Allow-Origin", "*") // 允许所有来源，或指定具体的来源
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		// 预检请求直接返回200状态码
		w.WriteHeader(http.StatusOK)
		return
	}

	// 如果不是POST请求，则返回405 Method Not Allowed
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Filename  string `json:"filename"`
		Total     int    `json:"total"`
		OutputDir string `json:"outputDir"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	newFilename, success := mergeChunks(req.Filename, req.Total, req.OutputDir)
	if !success {
		http.Error(w, "Failed to merge chunks", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{
		"message":  "Upload completed",
		"filename": newFilename,
	})
}

func mergeChunks(filename string, total int, outputDir string) (string, bool) {
	lock.Lock()
	defer lock.Unlock()
	// 检查所有分片是否存在
	for i := 0; i < total; i++ {
		chunkPath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, i))
		if _, err := os.Stat(chunkPath); os.IsNotExist(err) {
			return "", false
		}
	}

	// 确保输出目录存在
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return "", false
	}

	finalFilePath := getUniqueFilePath(outputDir, filename)
	finalFile, err := os.Create(finalFilePath)
	if err != nil {
		fmt.Printf("Error creating final file: %v\n", err)
		return "", false
	}
	defer finalFile.Close()

	// 合并所有分片
	for i := 0; i < total; i++ {
		chunkPath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, i))
		chunkData, err := os.ReadFile(chunkPath)
		if err != nil {
			fmt.Printf("Error reading chunk %d: %v\n", i, err)
			return "", false
		}

		if _, err := finalFile.Write(chunkData); err != nil {
			fmt.Printf("Error writing chunk %d to final file: %v\n", i, err)
			return "", false
		}

		os.Remove(chunkPath)
	}

	return filepath.Base(finalFilePath), true
}

// 辅助函数：将字符串转换为整数
func atoi(s string) int {
	var n int
	fmt.Sscanf(s, "%d", &n)
	return n
}
