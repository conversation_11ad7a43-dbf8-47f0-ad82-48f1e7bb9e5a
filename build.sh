#!/bin/bash

set -xe
ARGOS=$(uname -r)
ARGTAG=$(git describe --abbrev=1  --tags)
ARGVERSION=$(git  log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10)
ARGDATE=$(git show -s --date=format:'%Y-%m-%d-%H:%M:%S' --format=%cd)
docker build  --no-cache -t tianwen1:5000/hci_agent:$ARGTAG --build-arg ARG_VERSION=$ARGVERSION --build-arg ARG_TAG=$ARGTAG  --build-arg ARG_COMMIT_DATE=$ARGDATE --build-arg ARG_OS=ARGOS .
docker push  tianwen1:5000/hci_agent:$ARGTAG
docker tag tianwen1:5000/hci_agent:$ARGTAG tianwen1:5000/hci_agent:latest
docker push tianwen1:5000/hci_agent:latest


# docker run  --name disk-exporter --net=host --privileged -v /proc:/proc -v /sys:/sys -v /:/rootfs -v /etc:/etc -v /dev:/dev -v /run/lvm:/run/lvm -t tianwen1:5000/disk-exporter:v1
