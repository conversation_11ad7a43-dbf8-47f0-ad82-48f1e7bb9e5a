package system

import (
	"fmt"
	"hci_agent/model"
	"os"
	"os/exec"
	"strings"
)

func collectUSBInfo(hwInfo *model.HardwareInfoDB) error {
	// 使用 lsusb -v 获取详细信息
	cmd := exec.Command("lsusb", "-v")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return err
	}

	lines := strings.Split(string(output), "\n")
	var currentDevice *model.UsbDB

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "Bus") {
			// 如果存在前一个设备，保存它
			if currentDevice != nil {
				if err := currentDevice.CreateOrUpdate(); err != nil {
					return fmt.Errorf("failed to save USB device %s: %v", currentDevice.Device, err)
				}
			}

			// 解析新设备信息
			// 格式: Bus 001 Device 002: ID 8087:0024 Intel Corp.
			parts := strings.Split(line, "ID ")
			if len(parts) > 1 {
				deviceInfo := strings.Split(parts[1], " ")
				if len(deviceInfo) > 0 {
					deviceID := deviceInfo[0]
					name := strings.Join(deviceInfo[1:], " ")

					currentDevice = &model.UsbDB{
						HardwareInfoID: hwInfo.ID,
						Device:         deviceID,
						Name:           name,
						Status:         "connected", // 默认状态
					}
				}
			}
		} else if currentDevice != nil {
			// 解析设备的附加信息
			switch {
			case strings.HasPrefix(line, "iProduct"):
				currentDevice.Description = strings.TrimSpace(strings.Split(line, "iProduct")[1])
			case strings.Contains(line, "Driver="):
				parts := strings.Split(line, "Driver=")
				if len(parts) > 1 {
					currentDevice.PciPath = strings.TrimSpace(parts[1])
				}
			}
		}
	}

	// 保存最后一个设备
	if currentDevice != nil {
		if err := currentDevice.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save USB device %s: %v", currentDevice.Device, err)
		}
	}

	return nil
}

type lsusbDevice struct {
	description string
}

func getLSUSBInfo() (map[string]lsusbDevice, error) {
	cmd := exec.Command("lsusb", "-v")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	devices := make(map[string]lsusbDevice)
	lines := strings.Split(string(output), "\n")
	var currentDevice lsusbDevice
	var currentID string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Bus") {
			if currentID != "" {
				devices[currentID] = currentDevice
			}
			// Extract ID from line like "Bus 001 Device 002: ID 8087:0024"
			parts := strings.Split(line, "ID ")
			if len(parts) > 1 {
				currentID = strings.Split(parts[1], " ")[0]
				currentDevice = lsusbDevice{}
			}
		} else if strings.HasPrefix(line, "iProduct") {
			currentDevice.description = strings.TrimSpace(strings.Split(line, "iProduct")[1])
		}
	}

	if currentID != "" {
		devices[currentID] = currentDevice
	}

	return devices, nil
}

func getUSBDeviceInfo(address string) (status string, pciPath string) {
	// 检查设备状态
	statusPath := fmt.Sprintf("/sys/bus/usb/devices/%s/power/runtime_status", address)
	if data, err := os.ReadFile(statusPath); err == nil {
		status = strings.TrimSpace(string(data))
	} else {
		status = "unknown"
	}

	// 查找关联的 PCI 路径
	cmd := exec.Command("udevadm", "info", "--query=path", "--name", fmt.Sprintf("/dev/bus/usb/%s", address))
	output, err := cmd.CombinedOutput()
	if err == nil {
		paths := strings.Split(string(output), "/")
		for _, path := range paths {
			if strings.HasPrefix(path, "pci") {
				pciPath = path
				break
			}
		}
	}

	return status, pciPath
}
