package system

import (
	"encoding/json"
	"fmt"
	"hci_agent/model"
	"hci_agent/pkg/utils"
	"net/http"
)

func HandlerCpuInfo(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		//获取params
		ip := r.URL.Query().Get("ip")

		if refreshCpuInfo(w, ip) {
			return
		}

		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		w.Write<PERSON>eader(http.StatusOK)
		fmt.Println("刷新host硬件信息完成")
		json.NewEncoder(w).Encode(map[string]string{"msg": "ok"})

	} else {
		//w.WriteHeader(http.StatusOK)
		//fmt.Fprintln(w, "upload file ....")
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)

	}

}

func refreshCpuInfo(w http.ResponseWriter, ip string) bool {
	libvirtConn, _ := utils.GetLibvirtConn("")
	hostInfo, err := utils.GetHostInfo(libvirtConn)
	if err != nil {
		fmt.Println("存储池刷新失败", err.Error())
	}

	// 获取存储卷信息，保存到数据库
	var host model.Host
	host.Ip = ip

	h, err := host.GetWithIp()
	h.IOMMUStatus = hostInfo.IOMMUStatus
	if hostInfo.ConnectionStatus {
		h.IsConnected = 1
	} else {
		h.IsConnected = 0
	}
	h.IsMaintain = hostInfo.MaintenanceMode
	h.NumaCell = hostInfo.NumaNodes
	h.CpuSockets = hostInfo.CPUSockets
	h.CpuThreadsPerCore = hostInfo.ThreadsPerCore
	h.CpuCoresPerSocket = hostInfo.CorePerSocket
	h.IscsiInitiator = hostInfo.ISCSIInitiator
	h.SerialNumber = hostInfo.SerialNumber

	err = h.Update()
	if err := CollectAndSaveHardwareInfo(h.ID); err != nil {
	}

	if err != nil {
		fmt.Println("获取host硬件信息失败：", err.Error())
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return true
	}
	return false
}
