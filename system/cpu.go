package system

import (
	"fmt"
	"hci_agent/model"
	"os/exec"
	"strings"

	"github.com/shirou/gopsutil/v3/cpu"
)

func collectCPUInfo(hwInfo *model.HardwareInfoDB) error {
	// 获取基本 CPU 信息
	cpuInfo, err := cpu.Info()
	if err != nil {
		return err
	}

	if len(cpuInfo) == 0 {
		return fmt.Errorf("no CPU info available")
	}

	// 获取核心数和线程数
	cores, err := cpu.Counts(true)
	if err != nil {
		return err
	}

	threads, err := cpu.Counts(false)
	if err != nil {
		return err
	}

	// 获取缓存信息
	l1, l2, l3 := getCPUCacheInfo()

	info := model.CPUInfoDB{
		HardwareInfoID: hwInfo.ID,
		Model:          cpuInfo[0].ModelName,
		Frequency:      fmt.Sprintf("%.0f MHz", cpuInfo[0].Mhz),
		Cores:          cores,
		Threads:        threads,
		CacheL1:        l1,
		CacheL2:        l2,
		CacheL3:        l3,
	}

	if err := info.CreateOrUpdate(); err != nil {
		return fmt.Errorf("failed to save CPU info: %v", err)
	}
	return nil
}

func getCPUCacheInfo() (l1, l2, l3 string) {
	// 使用 lscpu 获取缓存信息
	cmd := exec.Command("lscpu")
	output, err := cmd.Output()
	if err != nil {
		return "N/A", "N/A", "N/A"
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		switch {
		case strings.HasPrefix(line, "L1d cache:"):
			l1 = strings.TrimPrefix(line, "L1d cache:")
			l1 = strings.TrimSpace(l1)
		case strings.HasPrefix(line, "L2 cache:"):
			l2 = strings.TrimPrefix(line, "L2 cache:")
			l2 = strings.TrimSpace(l2)
		case strings.HasPrefix(line, "L3 cache:"):
			l3 = strings.TrimPrefix(line, "L3 cache:")
			l3 = strings.TrimSpace(l3)
		}
	}

	return
}
