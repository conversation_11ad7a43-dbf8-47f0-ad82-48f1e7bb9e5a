package system

import (
	"fmt"
	"hci_agent/model"
	"os"
	"os/exec"
	"strings"

	"github.com/jaypipes/ghw"
)

func collectPCIInfo(hwInfo *model.HardwareInfoDB) error {
	// 使用 ghw 获取基本 PCI 信息
	pci, err := ghw.PCI()
	if err != nil {
		return err
	}

	// 获取 lspci 详细信息
	lspciInfo, err := getLSPCIInfo()
	if err != nil {
		return err
	}

	for _, device := range pci.Devices {
		// 获取设备状态
		status := "unknown"
		if devicePath := fmt.Sprintf("/sys/bus/pci/devices/%s/power/runtime_status", device.Address); fileExists(devicePath) {
			if data, err := os.ReadFile(devicePath); err == nil {
				status = strings.TrimSpace(string(data))
			}
		}

		// 查找 lspci 中的详细信息
		deviceType := "unknown"
		if info, exists := lspciInfo[device.Address]; exists {
			deviceType = info.deviceType
		}

		pciDevice := model.PCIDeviceDB{
			HardwareInfoID: hwInfo.ID,
			DeviceID:       device.Address,
			Name:           device.Product.Name,
			Type:           deviceType,
			Status:         status,
		}

		if err := pciDevice.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save PCI device %s: %v", device.Address, err)
		}
	}
	return nil
}

type lspciDevice struct {
	deviceType string
}

func getLSPCIInfo() (map[string]lspciDevice, error) {
	cmd := exec.Command("lspci", "-vmm")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	devices := make(map[string]lspciDevice)
	lines := strings.Split(string(output), "\n")
	var currentDevice lspciDevice
	var currentAddress string

	for _, line := range lines {
		if strings.HasPrefix(line, "Slot:") {
			if currentAddress != "" {
				devices[currentAddress] = currentDevice
			}
			currentAddress = strings.TrimSpace(strings.TrimPrefix(line, "Slot:"))
			currentDevice = lspciDevice{}
		} else if strings.HasPrefix(line, "Class:") {
			currentDevice.deviceType = strings.TrimSpace(strings.TrimPrefix(line, "Class:"))
		}
	}

	if currentAddress != "" {
		devices[currentAddress] = currentDevice
	}

	return devices, nil
}

func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}
