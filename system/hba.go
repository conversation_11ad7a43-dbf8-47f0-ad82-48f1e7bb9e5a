package system

import (
	"fmt"
	"hci_agent/model"
	"os"
	"os/exec"
	"strings"
)

// HBA device information structure
type hbaDevice struct {
	name   string
	wwn    string
	wwpn   string
	vendor string
	model  string
}

func collectHBAInfo(hwInfo *model.HardwareInfoDB) error {
	hbaDevices, err := getHBADevicesFromSystool()
	if err != nil {
		hbaDevices, err = getHBADevicesFromLs()
		if err != nil {
			return err
		}
	}

	for _, hba := range hbaDevices {
		hbaDevice := model.HBADeviceDB{
			HardwareInfoID: hwInfo.ID,
			Name:           hba.name,
			WWN:            hba.wwn,
			WWPN:           hba.wwpn,
			Vendor:         hba.vendor,
			Model:          hba.model,
		}
		if err := hbaDevice.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save HBA device %s: %v", hba.name, err)
		}
	}
	return nil
}

func getHBADevicesFromSystool() ([]hbaDevice, error) {
	cmd := exec.Command("systool", "-c", "fc_host", "-v")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	devices := make([]hbaDevice, 0)
	currentDevice := hbaDevice{}
	lines := strings.Split(string(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Class Device =") {
			if currentDevice.name != "" {
				devices = append(devices, currentDevice)
			}
			currentDevice = hbaDevice{
				name: strings.TrimPrefix(line, "Class Device = "),
			}
		} else if strings.Contains(line, "port_name") {
			currentDevice.wwn = strings.TrimSpace(strings.Split(line, "=")[1])
		} else if strings.Contains(line, "port_wwpn") {
			currentDevice.wwpn = strings.TrimSpace(strings.Split(line, "=")[1])
		}

		// Get vendor and model from device path
		if currentDevice.name != "" {
			devicePath := fmt.Sprintf("/sys/class/fc_host/%s/device", currentDevice.name)
			if vendorBytes, err := os.ReadFile(devicePath + "/vendor"); err == nil {
				currentDevice.vendor = strings.TrimSpace(string(vendorBytes))
			}
			if modelBytes, err := os.ReadFile(devicePath + "/model"); err == nil {
				currentDevice.model = strings.TrimSpace(string(modelBytes))
			}
		}
	}

	if currentDevice.name != "" {
		devices = append(devices, currentDevice)
	}

	return devices, nil
}

func getHBADevicesFromLs() ([]hbaDevice, error) {
	cmd := exec.Command("ls", "-l", "/sys/class/fc_host/")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	devices := make([]hbaDevice, 0)
	lines := strings.Split(string(output), "\n")

	for _, line := range lines {
		if strings.Contains(line, "host") {
			fields := strings.Fields(line)
			if len(fields) > 8 {
				hostName := fields[8]
				device := hbaDevice{
					name: hostName,
				}

				basePath := fmt.Sprintf("/sys/class/fc_host/%s", hostName)
				devicePath := fmt.Sprintf("%s/device", basePath)

				// Read WWN
				if wwn, err := os.ReadFile(basePath + "/port_name"); err == nil {
					device.wwn = strings.TrimSpace(string(wwn))
				}

				// Read WWPN
				if wwpn, err := os.ReadFile(basePath + "/port_wwpn"); err == nil {
					device.wwpn = strings.TrimSpace(string(wwpn))
				}

				// Read vendor
				if vendor, err := os.ReadFile(devicePath + "/vendor"); err == nil {
					device.vendor = strings.TrimSpace(string(vendor))
				}

				// Read model
				if model, err := os.ReadFile(devicePath + "/model"); err == nil {
					device.model = strings.TrimSpace(string(model))
				}

				devices = append(devices, device)
			}
		}
	}

	return devices, nil
}
