package system

import (
	"fmt"
	"hci_agent/model"

	"github.com/jaypipes/ghw"
	"github.com/shirou/gopsutil/v3/disk"
)

func collectDiskInfo(hwInfo *model.HardwareInfoDB) error {
	// 使用 ghw 获取详细的磁盘信息
	block, err := ghw.Block()
	if err != nil {
		return err
	}

	// 获取 gopsutil 的磁盘状态信息
	diskStats, err := disk.IOCounters()
	if err != nil {
		return err
	}

	for _, disk := range block.Disks {
		// 计算磁盘大小为GB
		sizeGB := disk.SizeBytes / (1024 * 1024 * 1024)

		// 获取磁盘状态
		status := "unknown"
		if stats, exists := diskStats[disk.Name]; exists {
			if stats.IoTime > 0 {
				status = "active"
			} else {
				status = "idle"
			}
		}

		diskInfo := model.DiskDB{
			HardwareInfoID: hwInfo.ID,
			Device:         disk.Name,
			Size:           sizeGB,
			Vendor:         disk.Vendor,
			Model:          disk.Model,
			Status:         status,
		}

		if err := diskInfo.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save disk %s: %v", disk.Name, err)
		}
	}
	return nil
}
