package system

import (
	"fmt"
	"hci_agent/model"
	"os/exec"
	"strings"
)

func collectGPUInfo(hwInfo *model.HardwareInfoDB) error {
	// 检查 nvidia-smi 是否可用
	if !isNvidiaSmiAvailable() {
		return nil // 如果没有 NVIDIA GPU，静默返回
	}

	// 获取 GPU 信息，使用更通用的查询字段
	cmd := exec.Command("nvidia-smi", "--query-gpu=gpu_name,uuid,pci.device_id", "--format=csv,noheader,nounits")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to get GPU info: %v", err)
	}

	// 解析每个 GPU 的信息
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if len(strings.TrimSpace(line)) == 0 {
			continue
		}

		fields := strings.Split(line, ", ")
		if len(fields) < 3 {
			continue
		}

		gpuInfo := model.GPUInfoDB{
			HardwareInfoID: hwInfo.ID,
			Model:          strings.TrimSpace(fields[0]),
			Vendor:         "NVIDIA",
			DeviceID:       strings.TrimSpace(fields[2]),
			// vGPU 相关字段设置为默认值
			UsedVgpus:      0,
			AvailableVgpus: 0,
		}

		if err := gpuInfo.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save GPU info: %v", err)
		}
	}

	return nil
}

func isNvidiaSmiAvailable() bool {
	cmd := exec.Command("which", "nvidia-smi")
	return cmd.Run() == nil
}
