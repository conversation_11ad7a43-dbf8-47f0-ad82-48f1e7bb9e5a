package system

import (
	"fmt"
	"hci_agent/model"
	"os/exec"
	"strings"

	"github.com/jaypipes/ghw"
	"github.com/shirou/gopsutil/v3/net"
)

func collectNetworkInfo(hwInfo *model.HardwareInfoDB) error {
	interfaces, err := net.Interfaces()
	if err != nil {
		return err
	}

	// 预先获取所有网卡的详细信息
	networkInfo, err := ghw.Network()
	if err == nil {
		nicDetails := make(map[string]*ghw.NIC)
		for _, nic := range networkInfo.NICs {
			nicDetails[nic.Name] = nic
		}

		for _, iface := range interfaces {
			if shouldFilterInterface(iface.Name) {
				continue
			}

			// 获取网卡类型和速率信息
			var nicType, speed, duplex string
			if nic, exists := nicDetails[iface.Name]; exists {
				nicType = "physical"
				if nic.IsVirtual {
					nicType = "virtual"
				}
				speed = nic.Speed
				duplex = nic.Duplex
			}

			// 执行 ethtool 命令获取更多详细信息
			mtu := iface.MTU
			linkStatus := "unknown"
			if status := getEthtoolInfo(iface.Name); status != nil {
				if status.Link {
					linkStatus = "up"
				} else {
					linkStatus = "down"
				}
			}

			networkCard := model.NetworkCardDB{
				HardwareInfoID: hwInfo.ID,
				Name:           iface.Name,
				MAC:            iface.HardwareAddr,
				Speed:          speed,
				Status:         getInterfaceStatus(iface.Flags),
				Type:           nicType,
				MTU:            mtu,
				LinkStatus:     linkStatus,
				Duplex:         duplex,
				IPAddress:      getInterfaceIPs(iface),
			}

			if err := networkCard.CreateOrUpdate(); err != nil {
				return fmt.Errorf("failed to save network card %s: %v", iface.Name, err)
			}
		}
	}

	return nil
}

type ethtoolInfo struct {
	Link   bool
	Speed  string
	Duplex string
}

func getEthtoolInfo(ifaceName string) *ethtoolInfo {
	cmd := exec.Command("ethtool", ifaceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil
	}

	info := &ethtoolInfo{}
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		switch {
		case strings.Contains(line, "Link detected:"):
			info.Link = strings.Contains(line, "yes")
		case strings.Contains(line, "Speed:"):
			info.Speed = strings.TrimSpace(strings.Split(line, ":")[1])
		case strings.Contains(line, "Duplex:"):
			info.Duplex = strings.TrimSpace(strings.Split(line, ":")[1])
		}
	}
	return info
}

func getInterfaceStatus(flags []string) string {
	for _, flag := range flags {
		if flag == "up" {
			return "up"
		}
	}
	return "down"
}

func getInterfaceIPs(iface net.InterfaceStat) string {
	addrs := make([]string, 0, len(iface.Addrs))
	for _, addr := range iface.Addrs {
		// Extract only the IP address part before the subnet mask
		ip := strings.Split(addr.Addr, "/")[0]
		addrs = append(addrs, ip)
	}

	// Return only the first IP address if available
	if len(addrs) > 0 {
		return addrs[0]
	}
	return ""
}

func shouldFilterInterface(name string) bool {
	// Filter virtual, loopback, and container interfaces
	prefixesToFilter := []string{
		"veth",    // Virtual Ethernet devices
		"br-",     // Bridge interfaces
		"docker",  // Docker interfaces
		"virbr",   // Virtual bridge interfaces
		"lo",      // Loopback
		"tap",     // TAP interfaces
		"cali",    // Calico interfaces
		"flannel", // Flannel interfaces
		"tun",     // TUN interfaces
		"dummy",   // Dummy interfaces
	}

	// Check interface type using ethtool
	isVirtual := false
	cmd := exec.Command("ethtool", "-i", name)
	output, err := cmd.CombinedOutput()
	if err == nil {
		isVirtual = strings.Contains(string(output), "driver: dummy") ||
			strings.Contains(string(output), "driver: veth") ||
			strings.Contains(string(output), "driver: bridge")
	}

	// Check prefixes
	for _, prefix := range prefixesToFilter {
		if strings.HasPrefix(name, prefix) {
			return true
		}
	}

	return isVirtual
}
