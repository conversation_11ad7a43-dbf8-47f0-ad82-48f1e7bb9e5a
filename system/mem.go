package system

import (
	"fmt"
	"hci_agent/model"
	"os/exec"
	"strconv"
	"strings"
)

type memoryModule struct {
	Manufacturer string
	PartNumber   string
	Type         string
	Size         uint64
}

func collectMemoryInfo(hwInfo *model.HardwareInfoDB) error {
	// 使用 dmidecode 获取详细内存信息
	cmd := exec.Command("dmidecode", "-t", "memory")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to get memory info: %v", err)
	}

	// 解析内存模块
	modules := parseMemoryModules(string(output))

	// 创建每个内存模块的记录
	for _, module := range modules {
		// 忽略空插槽
		if module.Size == 0 {
			continue
		}

		memInfo := model.MemoryInfoDB{
			HardwareInfoID: hwInfo.ID,
			Name:           module.PartNumber,
			Manufacturer:   module.Manufacturer,
			Size:           module.Size,
			Type:           module.Type,
		}

		if err := memInfo.CreateOrUpdate(); err != nil {
			return fmt.Errorf("failed to save memory module info: %v", err)
		}
	}

	return nil
}

func parseMemoryModules(output string) []memoryModule {
	lines := strings.Split(output, "\n")
	var modules []memoryModule
	var currentModule *memoryModule

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, "Memory Device") {
			if currentModule != nil {
				modules = append(modules, *currentModule)
			}
			currentModule = &memoryModule{}
			continue
		}

		if currentModule != nil {
			switch {
			case strings.Contains(line, "Manufacturer:"):
				currentModule.Manufacturer = strings.TrimSpace(strings.Split(line, ":")[1])
			case strings.Contains(line, "Part Number:"):
				currentModule.PartNumber = strings.TrimSpace(strings.Split(line, ":")[1])
			case strings.Contains(line, "Type:"):
				currentModule.Type = strings.TrimSpace(strings.Split(line, ":")[1])
			case strings.Contains(line, "Size:"):
				sizeStr := strings.TrimSpace(strings.Split(line, ":")[1])
				if strings.Contains(sizeStr, "MB") {
					size, _ := strconv.ParseUint(strings.Split(sizeStr, " ")[0], 10, 64)
					currentModule.Size = size * 1024 * 1024 // Convert MB to bytes
				} else if strings.Contains(sizeStr, "GB") {
					size, _ := strconv.ParseUint(strings.Split(sizeStr, " ")[0], 10, 64)
					currentModule.Size = size * 1024 * 1024 * 1024 // Convert GB to bytes
				}
			}
		}
	}

	if currentModule != nil {
		modules = append(modules, *currentModule)
	}

	return modules
}
