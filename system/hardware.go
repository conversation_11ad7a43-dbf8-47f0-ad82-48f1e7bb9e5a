package system

import (
	"fmt"
	"hci_agent/model"
	"os/exec"
	"time"
)

/*
# For RHEL/CentOS
yum install -y sysfsutils sg3_utils

# For Ubuntu/Debian
apt-get install -y sysfsutils sg3-utils

apt

	apt install -y dmidecode pciutils usbutils ethtool util-linux lshw systool
	apt install -y sysfsutils sg3-utils
	apt install -y nvidia-driver nvidia-smi

		apt-get install -y \
			dmidecode \     # 用于内存信息
			pciutils \      # lspci，用于PCI设备信息
			usbutils \      # lsusb，用于USB设备信息
			ethtool \       # 网卡信息
			util-linux \    # 基本系统工具
			lshw \          # 硬件列表工具
			systool        # 系统工具

	HBA卡信息收集需要

		apt-get install -y \
			sysfsutils \    # systool命令
			sg3-utils       # SCSI工具

	GPU信息收集

		# 安装NVIDIA驱动和工具
		apt-get install -y nvidia-driver nvidia-smi

yum

	yum install -y dmidecode pciutils usbutils ethtool util-linux lshw sysfsutils
	yum install -y sysfsutils sg3-utils
	yum install -y nvidia-driver nvidia-driver-cuda

	# Basic system tools

		yum install -y \
			dmidecode \     # For memory info
			pci-utils \     # For lspci
			usbutils \      # For lsusb
			ethtool \       # For network card info
			util-linux \    # Basic system tools
			lshw \          # Hardware list tool
			sysfsutils     # For systool

	# HBA card tools

		yum install -y \
			sg3_utils      # SCSI tools

	# NVIDIA GPU tools (RHEL/CentOS)
	# First, add NVIDIA repository
	dnf config-manager --add-repo https://developer.download.nvidia.com/compute/cuda/repos/rhel8/x86_64/cuda-rhel8.repo

	# Then install NVIDIA drivers and tools

		yum install -y \
			nvidia-driver \
			nvidia-driver-cuda
*/
func checkDependencies() error {
	dependencies := []string{
		"dmidecode",
		"lspci",
		"lsusb",
		"ethtool",
		"systool",
		"nvidia-smi",
	}

	for _, dep := range dependencies {
		cmd := exec.Command("which", dep)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("required dependency not found: %s", dep)
		}
	}
	return nil
}

func CollectAndSaveHardwareInfo(hostID string) error {
	hwInfo := &model.HardwareInfoDB{
		HostID:    hostID,
		CreateAt:  time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := hwInfo.CreateOrUpdate(); err != nil {
		return fmt.Errorf("failed to create/update hardware info: %v", err)
	}

	if err := checkDependencies(); err != nil {
		fmt.Printf("failed to check dependencies: %v", err)
	}

	collectors := []func(*model.HardwareInfoDB) error{
		collectNetworkInfo,
		collectDiskInfo,
		collectCPUInfo,
		collectMemoryInfo,
		collectHBAInfo,
		collectUSBInfo,
		collectGPUInfo,
		collectPCIInfo,
	}

	for _, collector := range collectors {
		if err := collector(hwInfo); err != nil {
			fmt.Printf("%T failed: %v", collector, err)
		}
	}

	return nil
}
