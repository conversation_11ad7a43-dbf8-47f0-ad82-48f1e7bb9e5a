package system

import (
	"fmt"
	"hci_agent/osd"
	"log"
	"net/http"
	"os"
	"os/exec"
)

func HandlerShutdown(w http.ResponseWriter, r *http.Request) {
	// 执行宿主机重启命令（需要特权或 root 权限）
	cmd := exec.Command("shutdown -h now")
	err := cmd.Run()
	if err != nil {
		fmt.Println("Error:", err)
		http.Error(w, "Failed to restart host", http.StatusInternalServerError)
		return
	}

	fmt.Println("Host is restarting...")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintln(w, "Host is restarting...")
}

func HandlerRestart(w http.ResponseWriter, r *http.Request) {
	// 执行宿主机重启命令（需要特权或 root 权限）
	cmd := exec.Command("reboot")
	err := cmd.Run()
	if err != nil {
		fmt.Println("Error:", err)
		http.Error(w, "Failed to restart host", http.StatusInternalServerError)
		return
	}

	fmt.Println("Host is restarting...")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintln(w, "Host is restarting...")
}

func HandlerMaintenance(w http.ResponseWriter, r *http.Request) {
	log.Println("Received shutdown request")
	err := SetMaintenance()
	if err != nil {
		log.Printf("Failed to shut down: %v\n", err)
		http.Error(w, "Failed to shut down", http.StatusInternalServerError)
		return
	}
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, "Shutting down...\n")
}

func Shutdown() error {
	fmt.Println("正在关闭服务器...")
	cmd := exec.Command("shutdown", "now")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func Restart() error {
	if os.Geteuid() != 0 {
		return fmt.Errorf("需要特权执行此操作")
	}
	fmt.Println("正在关闭服务器...")
	cmd := exec.Command("shutdown", "/r", "/t", "0")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()

}

func SetMaintenance() error {
	cmd := exec.Command("docker", "service", "ls")
	//cmd := exec.Command("docker", "service", "rm", "$(docker service ls)")
	return cmd.Run()
}

func HandlerOSDRestart(w http.ResponseWriter, r *http.Request) {
	// 从参数中获取 osd 名称
	osdName := r.URL.Query().Get("osd")
	if osdName == "" {
		http.Error(w, "osd 参数不能为空", http.StatusBadRequest)
		return
	}

	// 重启 osd
	go osd.MemoryLeaksRestart(osdName)

	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, "ok\n")
}
