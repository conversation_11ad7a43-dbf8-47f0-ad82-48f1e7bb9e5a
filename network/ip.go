package network

import (
	"fmt"
	"net"
)

// GetExternalIP 获取外网IP
func GetExternalIP() string {
	// 获取本机的默认路由
	ifaces, err := net.Interfaces()
	if err != nil {
		fmt.Printf("Error:Error getting interfaces: %s", err)
		return ""
	}

	for _, iface := range ifaces {
		addrs, err := iface.Addrs()
		if err != nil {
			fmt.Printf("Error: %s", err)
			return ""
		}

		for _, addr := range addrs {
			ip, _, err := net.ParseCIDR(addr.String())
			if err != nil {
				fmt.Printf("Error: %s", err)
				return ""
			}

			if ip.IsGlobalUnicast() && !ip.IsLoopback() && !ip.IsLinkLocalUnicast() {
				return ip.String()
			}
		}
	}
	fmt.Println("Error: Default route IP not found")
	return ""
}
