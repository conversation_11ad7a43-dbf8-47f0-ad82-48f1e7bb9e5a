package model

import "time"

// Host undefined
type Host struct {
	ID                    string    `json:"id" gorm:"id"`                                           // 主键
	PoolId                string    `json:"pool_id" gorm:"pool_id"`                                 // (外键)主机池
	ClusterId             string    `json:"cluster_id" gorm:"cluster_id"`                           // (外键)集群
	SrcClusterId          string    `json:"src_cluster_id" gorm:"src_cluster_id"`                   // (外键)原集群
	IsSupporting          int64     `json:"is_supporting" gorm:"is_supporting"`                     // 是否支援主机
	Ip                    string    `json:"ip" gorm:"ip"`                                           // ip地址
	Name                  string    `json:"name" gorm:"name"`                                       // 名称
	Remark                string    `json:"remark" gorm:"remark"`                                   // 备注
	SshUser               string    `json:"ssh_user" gorm:"ssh_user"`                               // ssh用户
	SshPassword           string    `json:"ssh_password" gorm:"ssh_password"`                       // ssh用户密码(aes加密)
	Port                  string    `json:"port" gorm:"port"`                                       // ssh端口
	Hostname              string    `json:"hostname" gorm:"hostname"`                               // 主机名称. hostname脚本获取
	HostModel             string    `json:"host_model" gorm:"host_model"`                           // 主机型号
	Mac                   string    `json:"mac" gorm:"mac"`                                         // mac地址
	CpuArchitecture       string    `json:"cpu_architecture" gorm:"cpu_architecture"`               // cpu架构
	CpuThreads            int64     `json:"cpu_threads" gorm:"cpu_threads"`                         // 逻辑cpu(线程)总数
	CpuThreadsPerCore     int64     `json:"cpu_threads_per_core" gorm:"cpu_threads_per_core"`       // cpu每物理核心线程数
	CpuCoresPerSocket     int64     `json:"cpu_cores_per_socket" gorm:"cpu_cores_per_socket"`       // cpu每socket物理核心数
	CpuSockets            int64     `json:"cpu_sockets" gorm:"cpu_sockets"`                         // cpu的socket数量
	CpuCores              int64     `json:"cpu_cores" gorm:"cpu_cores"`                             // cpu的core数量
	CpuModelName          string    `json:"cpu_model_name" gorm:"cpu_model_name"`                   // cpu的型号名称
	CpuFrequency          int64     `json:"cpu_frequency" gorm:"cpu_frequency"`                     // cpu频率(单位: MHz)
	CpuHfrequency         string    `json:"cpu_hfrequency" gorm:"cpu_hfrequency"`                   // cpu频率(human-readable)
	CpuVirtualization     string    `json:"cpu_virtualization" gorm:"cpu_virtualization"`           // cpu虚拟化( 如: VT-x ). lscpu不一定能拿到
	CpuVirtualizationType string    `json:"cpu_virtualization_type" gorm:"cpu_virtualization_type"` // cpu虚拟化类型( 如: full ). lscpu不一定能拿到
	Memory                int64     `json:"memory" gorm:"memory"`                                   // 内存大小(单位: byte).采用向下舍入floor规则
	Hmemory               string    `json:"hmemory" gorm:"hmemory"`                                 // 内存大小(human-readable)
	Storage               int64     `json:"storage" gorm:"storage"`                                 // 存储大小(本地 /cnware 分区大小)(单位: byte).采用向下舍入floor规则
	Hstorage              string    `json:"hstorage" gorm:"hstorage"`                               // 存储大小(human-readable)
	IsHa                  int64     `json:"is_ha" gorm:"is_ha"`                                     // 是否启用HA 0否 1是
	IsMaintain            int64     `json:"is_maintain" gorm:"is_maintain"`                         // 是否处于维护状态. 0否(平台可以对主机进行需要的操作), 1是(希望平台断开主机连接,不对主机进行任意操作)
	IsConnected           int64     `json:"is_connected" gorm:"is_connected"`                       // 是否连接上. 0否(在规定时间内,平台多次尝试,均未能连接上主机) 1是(连接正常)
	ConnOrDisconnTime     time.Time `json:"conn_or_disconn_time" gorm:"conn_or_disconn_time"`       // 连接或断开连接时间
	IsHaMigrated          int64     `json:"is_ha_migrated" gorm:"is_ha_migrated"`                   // 刚执行过HA迁移. 1是(触发HA迁移后会更新为1)  0否(重新连接会更新为0)
	WakeCategory          int64     `json:"wake_category" gorm:"wake_category"`                     // 0:网络唤醒,1:IPMI唤醒,2:禁用
	IpmiIpaddr            string    `json:"ipmi_ipaddr" gorm:"ipmi_ipaddr"`                         // 主机ilo地址
	IpmiUser              string    `json:"ipmi_user" gorm:"ipmi_user"`                             // ipmi用户
	IpmiPw                string    `json:"ipmi_pw" gorm:"ipmi_pw"`                                 // ipmi密码
	System                string    `json:"system" gorm:"system"`                                   // 主机系统
	Uptime                string    `json:"uptime" gorm:"uptime"`                                   // 主机系统启动时间(uptime -s)
	NumaCell              int64     `json:"numa_cell" gorm:"numa_cell"`                             // numa节点数
	CpuModelType          string    `json:"cpu_model_type" gorm:"cpu_model_type"`                   // cpu的型号类型
	Bmc                   string    `json:"bmc" gorm:"bmc"`                                         // 主机系统启动时间(uptime -s)
	Flags                 string    `json:"flags" gorm:"flags"`                                     // 标志寄存器
	KvVersion             string    `json:"kv_version" gorm:"kv_version"`                           // kv版本
	IscsiInitiator        string    `json:"iscsi_initiator" gorm:"iscsi_initiator"`                 // iscsi initiator name
	ReserveMemory         int64     `json:"reserve_memory" gorm:"reserve_memory"`                   // 预留内存配置
	SerialNumber          string    `json:"serial_number" gorm:"serial_number"`                     // 序列号
	PatchVersion          string    `json:"patch_version" gorm:"patch_version"`                     // 最新补丁版本号
	CreatedAt             time.Time `json:"created_at" gorm:"created_at"`
	DefenseStatus         int64     `json:"defense_status" gorm:"defense_status"` // ddos防御状态，写入1为开，0为关
	IOMMUStatus           string    `json:"iommu_status" json:"iommu_status"`
}

// TableName 表名称
func (h *Host) TableName() string {
	return "host"
}

func (h *Host) Get() (Host, error) {
	var host Host
	err := db.Where("id = ?", h.ID).First(&host).Error
	if err != nil {
		return Host{}, err
	}
	return host, nil
}

func (h *Host) GetWithIp() (Host, error) {
	var host Host
	err := db.Where("ip = ?", h.Ip).First(&host).Error
	if err != nil {
		return Host{}, err
	}
	return host, nil
}

func (h *Host) Create() error {
	err := db.Create(&h).Error
	if err != nil {
		return err
	}
	return nil
}

func (h *Host) Update() error {
	err := db.Save(&h).Error
	if err != nil {
		return err
	}
	return nil
}
