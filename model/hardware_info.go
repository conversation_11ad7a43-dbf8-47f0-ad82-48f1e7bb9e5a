package model

import (
	"github.com/google/uuid"
	"time"
)

type HardwareInfoDB struct {
	ID           string          `gorm:"type:varchar(36);primaryKey"`
	HostID       string          `gorm:"type:varchar(36);not null;index"`
	CreateAt     time.Time       `gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `gorm:"autoUpdateTime"`
	NetworkCards []NetworkCardDB `gorm:"foreignKey:HardwareInfoID"`
	Disks        []DiskDB        `gorm:"foreignKey:HardwareInfoID"`
	Usbs         []UsbDB         `gorm:"foreignKey:HardwareInfoID"`
	PCIDevices   []PCIDeviceDB   `gorm:"foreignKey:HardwareInfoID"`
	CPUInfo      []CPUInfoDB     `gorm:"foreignKey:HardwareInfoID"`
	MemoryInfo   []MemoryInfoDB  `gorm:"foreignKey:HardwareInfoID"`
	HBADevices   []HBADeviceDB   `gorm:"foreignKey:HardwareInfoID"`
	GPUs         []GPUInfoDB     `gorm:"foreignKey:HardwareInfoID"`
}

type NetworkCardDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Name           string `gorm:"type:varchar(255);not null"`
	Type           string `gorm:"type:varchar(50)"`
	MAC            string `gorm:"type:varchar(17)"`
	Speed          string `gorm:"type:varchar(50)"`
	Duplex         string `gorm:"type:varchar(50)"`
	Status         string `gorm:"type:varchar(50)"`
	MTU            int    `gorm:"type:int"`
	LinkStatus     string `gorm:"type:varchar(50)"`
	IPAddress      string `gorm:"type:varchar(39)"`
	Vendor         string `gorm:"type:varchar(255)"`
}

type DiskDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Device         string `gorm:"type:varchar(255);not null"`
	Size           uint64 `gorm:"type:bigint"`
	Vendor         string `gorm:"type:varchar(255)"`
	Model          string `gorm:"type:varchar(255)"`
	Status         string `gorm:"type:varchar(50)"`
}

type UsbDB struct {
	// 主键ID，使用UUID格式，长度36字符
	ID string `gorm:"type:varchar(36);primaryKey"`

	// 关联的硬件信息ID，用于建立与主硬件信息表的关系
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`

	// USB设备的标识符，通常是供应商ID和产品ID的组合 (如 "0951:1666")
	Device string `gorm:"type:varchar(50);not null"`

	// USB设备的名称，如制造商和产品名称
	Name string `gorm:"type:varchar(255)"`

	// 设备的详细描述信息，通常来自设备的iProduct字段
	Description string `gorm:"type:varchar(255)"`

	// 设备的当前状态（如 "connected", "disconnected" 等）
	Status string `gorm:"type:varchar(50)"`

	// 设备在PCI总线上的路径，用于定位设备的物理位置
	PciPath string `gorm:"type:varchar(255)"`
}

type PCIDeviceDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	DeviceID       string `gorm:"type:varchar(50);not null"`
	Name           string `gorm:"type:varchar(255)"`
	Type           string `gorm:"type:varchar(255)"`
	Status         string `gorm:"type:varchar(50)"`
}

type CPUInfoDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Model          string `gorm:"type:varchar(255)"`
	Frequency      string `gorm:"type:varchar(50)"`
	Threads        int    `gorm:"type:int"`
	Cores          int    `gorm:"type:int"`
	CacheL1        string `gorm:"type:varchar(50)"`
	CacheL2        string `gorm:"type:varchar(50)"`
	CacheL3        string `gorm:"type:varchar(50)"`
}

type MemoryInfoDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Name           string `gorm:"type:varchar(255)"`
	Manufacturer   string `gorm:"type:varchar(255)"`
	Size           uint64 `gorm:"type:bigint"`
	Type           string `gorm:"type:varchar(255)"`
}

type GPUInfoDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Model          string `gorm:"type:varchar(255)"`
	Vendor         string `gorm:"type:varchar(255)"`
	DeviceID       string `gorm:"type:varchar(255)"`
	UsedVgpus      int    `gorm:"type:int"`
	AvailableVgpus int    `gorm:"type:int"`
}

type HBADeviceDB struct {
	ID             string `gorm:"type:varchar(36);primaryKey"`
	HardwareInfoID string `gorm:"type:varchar(36);not null;index"`
	Name           string `gorm:"type:varchar(255);not null"`
	WWN            string `gorm:"type:varchar(255)"`
	WWPN           string `gorm:"type:varchar(255)"`
	Vendor         string `gorm:"type:varchar(255)"`
	Model          string `gorm:"type:varchar(255)"`
}

func (h *HardwareInfoDB) TableName() string {
	return "hw_hardware_info"
}

func (n *NetworkCardDB) TableName() string {
	return "hw_network_card"
}

func (d *DiskDB) TableName() string {
	return "hw_disk"
}

func (d *UsbDB) TableName() string {
	return "hw_usb"
}

func (p *PCIDeviceDB) TableName() string {
	return "hw_pci_device"
}

func (c *CPUInfoDB) TableName() string {
	return "hw_cpu_info"
}

func (m *MemoryInfoDB) TableName() string {
	return "hw_memory_info"
}

func (g *GPUInfoDB) TableName() string {
	return "hw_gpu_info"
}

func (h *HBADeviceDB) TableName() string {
	return "hw_hba_device"
}

// BeforeCreate hook to generate UUID before creating records
func (h *HardwareInfoDB) BeforeCreate() error {
	h.ID = uuid.New().String()
	return nil
}

func (n *NetworkCardDB) BeforeCreate() error {
	n.ID = uuid.New().String()
	return nil
}

func (d *DiskDB) BeforeCreate() error {
	d.ID = uuid.New().String()
	return nil
}

func (d *UsbDB) BeforeCreate() error {
	d.ID = uuid.New().String()
	return nil
}

func (p *PCIDeviceDB) BeforeCreate() error {
	p.ID = uuid.New().String()
	return nil
}

func (c *CPUInfoDB) BeforeCreate() error {
	c.ID = uuid.New().String()
	return nil
}

func (m *MemoryInfoDB) BeforeCreate() error {
	m.ID = uuid.New().String()
	return nil
}

func (g *GPUInfoDB) BeforeCreate() error {
	g.ID = uuid.New().String()
	return nil
}

func (h *HBADeviceDB) BeforeCreate() error {
	h.ID = uuid.New().String()
	return nil
}

// For HardwareInfoDB
func (h *HardwareInfoDB) CreateOrUpdate() error {
	var existing HardwareInfoDB
	result := db.Where("host_id = ?", h.HostID).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		h.ID = existing.ID
		return db.Save(h).Error
	}
	// Record doesn't exist, create new
	return db.Create(h).Error
}

// For NetworkCardDB
func (n *NetworkCardDB) CreateOrUpdate() error {
	var existing NetworkCardDB
	result := db.Where("hardware_info_id = ? AND name = ?", n.HardwareInfoID, n.Name).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		n.ID = existing.ID
		return db.Save(n).Error
	}
	// Record doesn't exist, create new
	return db.Create(n).Error
}

// For DiskDB
func (d *DiskDB) CreateOrUpdate() error {
	var existing DiskDB
	result := db.Where("hardware_info_id = ? AND device = ?", d.HardwareInfoID, d.Device).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		d.ID = existing.ID
		return db.Save(d).Error
	}
	// Record doesn't exist, create new
	return db.Create(d).Error
}

// For UsbDB
func (d *UsbDB) CreateOrUpdate() error {
	var existing UsbDB
	result := db.Where("hardware_info_id = ? AND device = ?", d.HardwareInfoID, d.Device).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		d.ID = existing.ID
		return db.Save(d).Error
	}
	// Record doesn't exist, create new
	return db.Create(d).Error
}

// For PCIDeviceDB
func (p *PCIDeviceDB) CreateOrUpdate() error {
	var existing PCIDeviceDB
	result := db.Where("hardware_info_id = ? AND device_id = ?", p.HardwareInfoID, p.DeviceID).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		p.ID = existing.ID
		return db.Save(p).Error
	}
	// Record doesn't exist, create new
	return db.Create(p).Error
}

// For CPUInfoDB
func (c *CPUInfoDB) CreateOrUpdate() error {
	var existing CPUInfoDB
	result := db.Where("hardware_info_id = ?", c.HardwareInfoID).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		c.ID = existing.ID
		return db.Save(c).Error
	}
	// Record doesn't exist, create new
	return db.Create(c).Error
}

// For MemoryInfoDB
func (m *MemoryInfoDB) CreateOrUpdate() error {
	var existing MemoryInfoDB
	result := db.Where("hardware_info_id = ?", m.HardwareInfoID).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		m.ID = existing.ID
		return db.Save(m).Error
	}
	// Record doesn't exist, create new
	return db.Create(m).Error
}

func (g *GPUInfoDB) CreateOrUpdate() error {
	var existing GPUInfoDB
	result := db.Where("hardware_info_id = ? AND device_id = ?", g.HardwareInfoID, g.DeviceID).First(&existing)
	if result.Error == nil {
		g.ID = existing.ID
		return db.Save(g).Error
	}
	return db.Create(g).Error
}

// For HBADeviceDB
func (h *HBADeviceDB) CreateOrUpdate() error {
	var existing HBADeviceDB
	result := db.Where("hardware_info_id = ? AND name = ?", h.HardwareInfoID, h.Name).First(&existing)
	if result.Error == nil {
		// Record exists, update it
		h.ID = existing.ID
		return db.Save(h).Error
	}
	// Record doesn't exist, create new
	return db.Create(h).Error
}
