package model

import (
	"context"
	"database/sql"
	"fmt"
	"hci_agent/pkg/setting"
	"log"
	"time"

	"github.com/canonical/go-dqlite/client"
	"github.com/canonical/go-dqlite/driver"
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	_ "github.com/lib/pq"
)

var db *gorm.DB

type Model struct {
	ID int `gorm:"column:id" db:"id" json:"id" form:"id"`
}

// Setup initializes the database instance
func Setup() {
	retryInterval := 20 * time.Second
	for {
		err := Database()
		if err != nil {
			log.Printf("models.Setup err:%s", err)
		}
		if err == nil {
			break
		}

		time.Sleep(retryInterval)
	}
}

// Database 初始化数据库
func Database() error {
	var err error
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var dns string

	done := make(chan struct{})
	go func() {
		defer close(done)
		switch setting.DatabaseSetting.Driver {
		case "kingbase":
			dns = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
				setting.DatabaseSetting.Host,
				setting.DatabaseSetting.User,
				setting.DatabaseSetting.Password,
				setting.DatabaseSetting.Name,
				setting.DatabaseSetting.Port)
			db, err = gorm.Open("postgres", dns)
		case "mysql":
			dns = fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local",
				setting.DatabaseSetting.User,
				setting.DatabaseSetting.Password,
				setting.DatabaseSetting.Host,
				setting.DatabaseSetting.Port,
				setting.DatabaseSetting.Name)
			db, err = gorm.Open("mysql", dns)
		case "dqlite":
			// 1. 创建一个包含 dqlite 节点地址的节点列表
			address := setting.DatabaseSetting.Host + ":" + setting.DatabaseSetting.Port
			dns = address + "/" + setting.DatabaseSetting.Name
			nodes := []client.NodeInfo{
				{ID: 1, Address: address},
			}

			// 2. 创建一个新的 dqlite 驱动
			store := client.NewInmemNodeStore()
			if err := store.Set(context.Background(), nodes); err != nil {
				log.Fatalf("Failed to set node info: %v", err)
				return
			}

			dqliteDriver, err := driver.New(store)
			if err != nil {
				log.Fatalf("无法创建 dqlite 驱动: %v", err)
			}

			// 3. 注册 dqlite 驱动
			sql.Register("dqlite", dqliteDriver)

			// 4. 使用 GORM 连接 dqlite
			// 注意：数据源名称应与 dqlite 驱动期望的格式一致
			db, err = gorm.Open("dqlite", setting.DatabaseSetting.Name)
			if err != nil {
				log.Fatalf("无法连接到 dqlite 数据库: %v", err)
			}

		}

	}()

	// 等待连接完成或超时
	select {
	case <-ctx.Done():
		// 连接超时
		return fmt.Errorf("database connection timed out")
	case <-done:
		// 连接已完成
		if err == nil {
			gorm.DefaultTableNameHandler = func(db *gorm.DB, defaultTableName string) string {
				return setting.DatabaseSetting.TablePrefix + defaultTableName
			}

			db.SingularTable(true)
			db.Callback().Create().Replace("gorm:update_time_stamp", updateTimeStampForCreateCallback)
			db.Callback().Update().Replace("gorm:update_time_stamp", updateTimeStampForUpdateCallback)
			db.Callback().Delete().Replace("gorm:delete", deleteCallback)
			db.DB().SetMaxIdleConns(10)
			db.DB().SetMaxOpenConns(100)
			db.DB().Ping()
			log.Printf("初始化%s数据库完成! dsn:%s", setting.DatabaseSetting.Driver, dns)
			// initMysqlTables(db)
		}

		return err
	}
}

// 数据库迁移
func initMysqlTables(db *gorm.DB) {
	db = db.AutoMigrate(
		&StoragePool{},
		&StorageVolume{},
	)

	if db.Error != nil {
		log.Println("数据表初始化失败: ", db.Error)
	}
}

// CloseDB closes database connection (unnecessary)
func CloseDB() {
	defer db.Close()
}

// updateTimeStampForCreateCallback will set `CreatedOn`, `ModifiedOn` when creating
func updateTimeStampForCreateCallback(scope *gorm.Scope) {
	if !scope.HasError() {
		nowTime := time.Now().Unix()
		if createTimeField, ok := scope.FieldByName("create_at"); ok {
			if createTimeField.IsBlank {
				createTimeField.Set(nowTime)
			}
		}

		if modifyTimeField, ok := scope.FieldByName("updated_at"); ok {
			if modifyTimeField.IsBlank {
				modifyTimeField.Set(nowTime)
			}
		}
	}
}

// updateTimeStampForUpdateCallback will set `ModifiedOn` when updating
func updateTimeStampForUpdateCallback(scope *gorm.Scope) {
	if _, ok := scope.Get("gorm:update_column"); !ok {
		scope.SetColumn("updated_at", time.Now())
	}
}

// deleteCallback will set `DeletedOn` where deleting
func deleteCallback(scope *gorm.Scope) {
	if !scope.HasError() {
		var extraOption string
		if str, ok := scope.Get("gorm:delete_option"); ok {
			extraOption = fmt.Sprint(str)
		}

		deletedOnField, hasDeletedOnField := scope.FieldByName("DeletedOn")

		if !scope.Search.Unscoped && hasDeletedOnField {
			scope.Raw(fmt.Sprintf(
				"UPDATE %v SET %v=%v%v%v",
				scope.QuotedTableName(),
				scope.Quote(deletedOnField.DBName),
				scope.AddToVars(time.Now().Unix()),
				addExtraSpaceIfExist(scope.CombinedConditionSql()),
				addExtraSpaceIfExist(extraOption),
			)).Exec()
		} else {
			scope.Raw(fmt.Sprintf(
				"DELETE FROM %v%v%v",
				scope.QuotedTableName(),
				addExtraSpaceIfExist(scope.CombinedConditionSql()),
				addExtraSpaceIfExist(extraOption),
			)).Exec()
		}
	}
}

// addExtraSpaceIfExist adds a separator
func addExtraSpaceIfExist(str string) string {
	if str != "" {
		return " " + str
	}
	return ""
}

// Paging 分页函数
func Paging(db *gorm.DB, page, pagesize int, result interface{}) error {

	//计算偏移量
	offset := (page - 1) * pagesize
	if err := db.Offset(offset).Limit(pagesize).Find(&result).Error; err != nil {
		return err
	}
	return nil
}
