package model

import (
	"time"
)

// StoragePool undefined
type StoragePool struct {
	ID              string    `json:"id" gorm:"id"`                               // 主键
	Name            string    `json:"name" gorm:"name"`                           // 存储池名称
	StorageDeviceId string    `json:"storage_device_id" gorm:"storage_device_id"` // 存储设备的ID
	StorageLocalDir string    `json:"storage_local_dir" gorm:"storage_local_dir"` // 本地存储目录
	TypeCode        string    `json:"type_code" gorm:"type_code"`                 // 存储类型代码
	TypeCodeDisplay string    `json:"type_code_display" gorm:"type_code_display"` // 存储类型显示名称
	UseType         int64     `json:"use_type" gorm:"use_type"`                   // 使用类型
	Status          int64     `json:"status" gorm:"status"`                       // 状态
	Capacity        int64     `json:"capacity" gorm:"capacity"`                   // 总容量，单位:字节
	Available       int64     `json:"available" gorm:"available"`                 // 可用容量，单位:字节
	Allocation      int64     `json:"allocation" gorm:"allocation"`               // 已分配容量，单位:字节
	Time            time.Time `json:"time" gorm:"time"`                           // 创建时间
	Remark          string    `json:"remark" gorm:"remark"`                       // 备注
}

// TableName 表名称
func (*StoragePool) TableName() string {
	return "storage_pool"
}

func (s *StoragePool) GetInfoById() (StoragePool, error) {

	err := db.Where("id = ?", s.ID).First(&s).Error
	if err != nil {
		return *s, err
	}

	return *s, nil
}

func (s *StoragePool) Get() ([]StoragePool, error) {

	r := make([]StoragePool, 0)
	err := db.Find(&r).Error
	if err != nil {
		return r, err
	}

	return r, nil
}