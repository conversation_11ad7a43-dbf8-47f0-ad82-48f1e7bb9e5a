package model

import "time"

// StorageVolume undefined
type StorageVolume struct {
	ID            string    `json:"id" gorm:"id"`                           // 主键
	StoragePoolId string    `json:"storage_pool_id" gorm:"storage_pool_id"` // 存储池id
	Name          string    `json:"name" gorm:"name"`                       // 存储卷名称(区分大小写)
	TypeCode          string    `json:"type_code" gorm:"type_code"`                       // 存储卷格式:存储卷类型:0.其他(libvirt剩余的) 1.qcow2 2.raw 3.iso, 4.vmdk, 5.vdi, 6.vhd, 7.qed, 8.dmg, 9.ova, 10.img
	JoinType      string    `json:"join_type" gorm:"join_type"`             // 加入平台的方式:1.同步加入,2平台添加,3模板磁盘/xml
	Path          string    `json:"path" gorm:"path"`                       // 存储卷路径
	Encrypt       int64     `json:"encrypt" gorm:"encrypt"`                 // 是否加密:0否 1 是
	Status        int64     `json:"status" gorm:"status"`                   // 状态:1.正常 2.异常
	Capacity      int64     `json:"capacity" gorm:"capacity"`               // 存储卷总容量
	Allocation    int64     `json:"allocation" gorm:"allocation"`           // 存储卷已用容量
	Preallocation string    `json:"preallocation" gorm:"preallocation"`     // 存储卷置备类型:1.off(精简置备) 2.falloc(厚置备延迟置零) 3.full(厚置备置零)
	Time          time.Time `json:"time" gorm:"time"`                       // 加入平台时间
	Remark        string    `json:"remark" gorm:"remark"`                   // 简要备注
}

// TableName 表名称
func (*StorageVolume) TableName() string {
	return "storage_volume"
}


func (s *StorageVolume) Create() error{
	err := db.Create(&s).Error
	if err != nil {
		return err
	}
	return nil
}
