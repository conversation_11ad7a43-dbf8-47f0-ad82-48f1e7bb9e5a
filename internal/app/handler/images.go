package handler

import (
	"fmt"
	"hci_agent/pkg/utils"
	"io"
	"os"
	"path/filepath"
	"sync"

	"github.com/gin-gonic/gin"
)

func HandlerImageUpload(c *gin.Context) {
	r := utils.GinTool{C: c}
	// TODO
	r.SuccessResponse("success", nil)
}

const (
	TEMP_DIR = "./chunks" // 分片临时存储路径
)

var (
	lock     sync.Mutex
	executor = make(chan struct{}, 5) // 控制并发数量
)

func init() {
	// 确保临时目录存在
	if err := os.MkdirAll(TEMP_DIR, 0755); err != nil {
		panic(fmt.Sprintf("Failed to create temp directory: %v", err))
	}
}

func saveChunk(filename string, index int, data []byte) {
	filepath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, index))
	err := os.WriteFile(filepath, data, 0644)
	if err != nil {
		fmt.Printf("Error saving chunk: %v\n", err)
		return
	}
	fmt.Printf("Saved chunk %d of %s\n", index, filename)
}

func HandleChunkUpload(c *gin.Context) {
	r := utils.GinTool{C: c}
	// 解析 multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil {
		r.FailResponse("Failed", err)
		return
	}

	// 获取文件块
	file, _, err := c.Request.FormFile("chunk")
	if err != nil {
		r.FailResponse("No chunk part", err)
		return
	}
	defer file.Close()

	// 读取表单数据
	index := c.PostForm("index")
	if index == "" {
		r.FailResponse("No index provided", nil)
		return
	}

	filename := c.PostForm("filename")
	if filename == "" {
		r.FailResponse("No filename provided", nil)
		return
	}

	// 读取文件数据
	data, err := io.ReadAll(file)
	if err != nil {
		r.FailResponse("Failed", err)
		return
	}

	// 使用 channel 控制并发
	executor <- struct{}{} // 获取执行许可
	go func() {
		defer func() { <-executor }() // 释放执行许可
		saveChunk(filename, atoi(index), data)
	}()

	// 返回成功响应
	r.SuccessResponse("Chunk received", nil)
}

func HandleComplete(c *gin.Context) {
	r := utils.GinTool{C: c}
	// 解析 JSON 请求体
	var req struct {
		Filename  string `json:"filename"`
		Total     int    `json:"total"`
		OutputDir string `json:"outputDir"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		r.FailResponse("参数绑定失败", err)
		return
	}

	// 合并文件块
	success := mergeChunks(req.Filename, req.Total, req.OutputDir)
	if !success {
		r.FailResponse("Failed to merge chunks", nil)
		return
	}

	// 返回成功响应
	r.SuccessResponse("Upload completed", nil)

}

func mergeChunks(filename string, total int, outputDir string) bool {
	lock.Lock()
	defer lock.Unlock()
	// 检查所有分片是否存在
	for i := 0; i < total; i++ {
		chunkPath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, i))
		if _, err := os.Stat(chunkPath); os.IsNotExist(err) {
			return false
		}
	}

	// 确保输出目录存在
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return false
	}

	finalFilePath := filepath.Join(outputDir, filename)
	// 创建最终文件
	finalFile, err := os.Create(finalFilePath)
	if err != nil {
		fmt.Printf("Error creating final file: %v\n", err)
		return false
	}
	defer finalFile.Close()

	// 合并所有分片
	for i := 0; i < total; i++ {
		chunkPath := filepath.Join(TEMP_DIR, fmt.Sprintf("%s.part%d", filename, i))
		chunkData, err := os.ReadFile(chunkPath)
		if err != nil {
			fmt.Printf("Error reading chunk %d: %v\n", i, err)
			return false
		}

		if _, err := finalFile.Write(chunkData); err != nil {
			fmt.Printf("Error writing chunk %d to final file: %v\n", i, err)
			return false
		}

		// 删除分片文件
		os.Remove(chunkPath)
	}

	return true
}

// 辅助函数：将字符串转换为整数
func atoi(s string) int {
	var n int
	fmt.Sscanf(s, "%d", &n)
	return n
}
