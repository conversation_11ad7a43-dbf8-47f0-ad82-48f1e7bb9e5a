package handler

import (
	"hci_agent/pkg/storage/iscsi"
	"hci_agent/pkg/utils"

	"github.com/gin-gonic/gin"
)

// 定义请求体结构体
type IscsiService struct {
	TargetIP  string `json:"target_ip"`
	TargetIQN string `json:"target_iqn"`
}

func DiscoverTargetsHandler(c *gin.Context) {
	// 获取请求参数

	r := utils.GinTool{C: c}

	// 解析 JSON 请求体
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	// 检查 target_ip 是否为空
	if req.TargetIP == "" {
		r.FailResponse("target_ip is required", nil)
		return
	}

	// 调用iscsi包的DiscoverTargets方法
	targets, err := iscsi.DiscoverTargets(req.TargetIP)
	if err != nil {
		r.FailResponse(err.Error(), nil)
		return
	}

	r.SuccessResponse("success", targets.Targets)
}

func LoginTargetHandler(c *gin.Context) {
	r := utils.GinTool{C: c}
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	if req.TargetIP == "" {
		r.FailResponse("target_ip is required", nil)
		return
	}

	if req.TargetIQN == "" {
		r.FailResponse("target_iqn is required", nil)
		return
	}

	devices, err := iscsi.LoginTargets(req.TargetIP, req.TargetIQN)
	if err != nil {
		r.FailResponse("Failed to login target", err)
		return
	}

	r.SuccessResponse("success", devices)
}


func DiscoverLoginTargetHandler(c *gin.Context){
	// 获取请求参数

	r := utils.GinTool{C: c}	

	// 解析 JSON 请求体
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	// 检查 target_ip 是否为空
	if req.TargetIP == "" {
		r.FailResponse("target_ip is required", nil)
		return
	}

	// 调用iscsi包的DiscoverTargets方法
	targets, err := iscsi.DiscoverTargets(req.TargetIP)
	if err != nil {
		r.FailResponse(err.Error(), nil)
		return
	}

	r.SuccessResponse("success", targets.Targets)
}

func GetNodesHandler(c *gin.Context){
	r := utils.GinTool{C: c}

	nodes, err := iscsi.GetNodes()
	if err != nil {
		r.FailResponse("Failed to get nodes", err)
		return
	}

	r.SuccessResponse("success", nodes)
}

func LogoutTargetHandler(c *gin.Context) {
	r := utils.GinTool{C: c}
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	if req.TargetIQN == "" {
		r.FailResponse("target_iqn is required", nil)
		return
	}

	err := iscsi.Logout(req.TargetIQN)
	if err != nil {
		r.FailResponse("Failed to logout target", err)
		return
	}
	r.SuccessResponse("success", nil)
}

func DeleteNodeHandler(c *gin.Context) {
	r := utils.GinTool{C: c}
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	if req.TargetIQN == "" {
		r.FailResponse("target_iqn is required", nil)
		return
	}

	err := iscsi.Logout(req.TargetIQN)
	if err != nil {
		r.FailResponse("Failed to delete target", err)
		return
	}
	r.SuccessResponse("success", nil)
}

func GetSessionsHandler(c *gin.Context) {
	r := utils.GinTool{C: c}

	sessions, err := iscsi.GetSessions()
	if err != nil {
		r.FailResponse("Failed to get sessions", err)
		return
	}

	r.SuccessResponse("success", sessions)
}

func GetDeviceTargetHandler(c *gin.Context) {
	r := utils.GinTool{C: c}
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	if req.TargetIQN == "" {
		r.FailResponse("target_iqn is required", nil)
		return
	}

	ret, err := iscsi.GetDevicesByTarget(req.TargetIQN)
	if err != nil {
		r.FailResponse("Failed to logout target", err)
		return
	}
	r.SuccessResponse("success", ret)
}

func GetSessionsInfoHandler(c *gin.Context) {
	r := utils.GinTool{C: c}
	var req IscsiService
	if err := c.BindJSON(&req); err != nil {
		r.FailResponse("Invalid request body", nil)
		return
	}

	// if req.TargetIQN == "" {
	// 	r.FailResponse("target_iqn is required", nil)
	// 	return
	// }

	ret, err := iscsi.GetSessionInfo(req.TargetIQN)
	if err != nil {
		r.FailResponse("Failed to logout target", err)
		return
	}
	r.SuccessResponse("success", ret)
}
