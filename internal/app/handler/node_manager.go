package handler

import (
	"hci_agent/osd"
	"hci_agent/pkg/utils"
	"hci_agent/system"

	"github.com/gin-gonic/gin"
)

func Maintenance(c *gin.Context) {
	r := utils.GinTool{C: c}
	err := system.SetMaintenance()
	if err != nil {
		r.<PERSON>ailResponse("Failed", err)
		return
	}
	r.SuccessResponse("success", nil)

}

func HandlerOSDRestart(c *gin.Context) {
	r := utils.GinTool{C: c}
	// 从参数中获取 osd 名称
	osdName := c.Query("osd")
	if osdName == "" {
		r.FailResponse("osd name is required", nil)
		return
	}

	// 重启 osd
	go osd.MemoryLeaksRestart(osdName)

	r.SuccessResponse("success", nil)
}
