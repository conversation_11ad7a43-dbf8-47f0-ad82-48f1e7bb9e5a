package handler

import (
	"fmt"
	"hci_agent/model"
	"hci_agent/pkg/utils"
	"hci_agent/system"

	"github.com/gin-gonic/gin"
)

func HandlerCpuInfo(c *gin.Context) {
	//TODO 数据库操作暂未补充
	r := utils.GinTool{C: c}
	// 获取查询参数
	ip := c.Query("ip")

	// 调用刷新 CPU 信息的逻辑
	if refreshCpuInfo(r, ip) {
		return
	}

	// 返回成功响应
	r.SuccessResponse("success", nil)
}

func refreshCpuInfo(r utils.GinTool, ip string) bool {
	libvirtConn, _ := utils.GetLibvirtConn("")
	hostInfo, err := utils.GetHostInfo(libvirtConn)
	if err != nil {
		fmt.Println("存储池刷新失败", err.Error())
	}

	// 获取存储卷信息，保存到数据库
	var host model.Host
	host.Ip = ip

	h, err := host.GetWithIp()
	if err != nil {
		fmt.Println("获取host数据库信息失败：", err.Error())
		r.FailResponse("获取host硬件信息失败: ", err)
		return true
	}
	h.IOMMUStatus = hostInfo.IOMMUStatus
	if hostInfo.ConnectionStatus {
		h.IsConnected = 1
	} else {
		h.IsConnected = 0
	}
	h.IsMaintain = hostInfo.MaintenanceMode
	h.NumaCell = hostInfo.NumaNodes
	h.CpuSockets = hostInfo.CPUSockets
	h.CpuThreadsPerCore = hostInfo.ThreadsPerCore
	h.CpuCoresPerSocket = hostInfo.CorePerSocket
	h.IscsiInitiator = hostInfo.ISCSIInitiator
	h.SerialNumber = hostInfo.SerialNumber

	err = h.Update()
	if err := system.CollectAndSaveHardwareInfo(h.ID); err != nil {
	}

	if err != nil {
		fmt.Println("获取host硬件信息失败：", err.Error())
		r.FailResponse("获取host硬件信息失败: ", err)

		return true
	}
	return false
}
