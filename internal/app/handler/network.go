package handler

import (
	"hci_agent/pkg/utils"
	"net"
	"os"

	"github.com/gin-gonic/gin"
)

func HandlerNic(c *gin.Context) {
	r := utils.GinTool{C: c}

	// 获取系统的所有网络接口
	interfaces, err := net.Interfaces()
	if err != nil {
		r.FailResponse("Unable to retrieve network interfaces", err)
		return
	}

	// 创建一个字符串切片存储物理接口名称
	var physicalInterfaceNames []string
	for _, iface := range interfaces {
		if isPhysicalInterface(iface) {
			physicalInterfaceNames = append(physicalInterfaceNames, iface.Name)
		}
	}

	r.SuccessResponse("success", physicalInterfaceNames)
}

func isPhysicalInterface(iface net.Interface) bool {
	// 检查硬件地址是否存在且不为空，并且不为回环接口
	if iface.Flags&net.FlagLoopback == 0 && iface.HardwareAddr != nil && len(iface.HardwareAddr) > 0 {
		// 检查是否存在 /sys/class/net/<interface>/device 文件夹
		if _, err := os.Stat("/sys/class/net/" + iface.Name + "/device"); os.IsNotExist(err) {
			return false
		}
		return true
	}
	return false
}
