package handler

import (
	"fmt"
	"hci_agent/pkg/utils"
	"os/exec"

	"github.com/gin-gonic/gin"
)

// HandlerRestart 重启主机
func HandlerRestart(c *gin.Context) {

	r := utils.GinTool{C: c}

	// 执行宿主机重启命令（需要特权或 root 权限）
	cmd := exec.Command("reboot")
	err := cmd.Run()
	if err != nil {
		fmt.Println("Error:", err)
		r.FailResponse("主机重启失败", err)
		return
	}

	r.SuccessResponse("主机正在重启", nil)
}

// HandlerShutdown 关闭主机
func HandlerShutdown(c *gin.Context) {

	r := utils.GinTool{C: c}

	// 执行宿主机关机命令（需要特权或 root 权限）
	cmd := exec.Command("shutdown", "-h", "now")
	err := cmd.Run()
	if err != nil {
		fmt.Println("Error:", err)
		r.FailResponse("主机重启失败", err)
		return
	}

	r.SuccessResponse("主机正在重启", nil)
}
