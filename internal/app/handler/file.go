package handler

import (
	"hci_agent/pkg/utils"
	"io"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

func HandlerUpload(c *gin.Context) {
	var r utils.GinTool

	// 解析 multipart 表单，限制文件大小为 10 MB
	err := c.Request.ParseMultipartForm(10 << 20)
	if err != nil {
		r.Fail<PERSON>esponse("Failed", err)
		return
	}

	// 获取上传的文件
	file, handler, err := c.Request.FormFile("file")
	if err != nil {
		r.FailResponse("Error retrieving the file", err)
		return
	}
	defer file.Close()

	// 创建 uploads 目录（如果不存在）
	if _, err := os.Stat("uploads"); os.IsNotExist(err) {
		os.Mkdir("uploads", 0755)
	}

	// 在服务器上创建文件
	dst, err := os.Create(filepath.Join("uploads", handler.Filename))
	if err != nil {
		r.FailResponse("Error creating the file", err)
		return
	}
	defer dst.Close()

	// 将上传的文件内容复制到服务器上的文件
	_, err = io.Copy(dst, file)
	if err != nil {
		r.FailResponse("Error saving the file", err)
		return
	}

	// 返回成功消息
	r.SuccessResponse("File uploaded successfully", handler.Filename)
}
