package handler

import (
	"context"
	"hci_agent/pkg/network/ovs"
	"hci_agent/pkg/utils"

	"github.com/gin-gonic/gin"
)

type BridgeReq struct {
	Name string `json:"name"`
}

// 网桥管理
func CreateBridgeHandler(c *gin.Context) {}
func ListBridgesHandler(c *gin.Context) {

	res := utils.GinTool{C: c}
	// 获取所有网桥
	client, err := ovs.GetOvsConnection("")
	if err != nil {
		res.FailResponse("Failed to connect to OVS", nil)
		return
	}
	defer client.Disconnect()
	client.MonitorAll(context.TODO())

	bridges, err := ovs.GetBridges(client)
	if err != nil {
		res.FailResponse("Failed to list bridges", nil)
		return
	}

	res.SuccessResponse("success", bridges)

}
func GetBridgeHandler(c *gin.Context)    {}
func UpdateBridgeHandler(c *gin.Context) {}
func DeleteBridgeHandler(c *gin.Context) {
	res := utils.GinTool{C: c}
	var bridge BridgeReq

	if err := c.ShouldBindJSON(&bridge); err != nil {
		res.FailResponse("Invalid request parameters", err)
		return
	}

	if bridge.Name == "" {
		res.FailResponse("Bridge name is required", nil)
		return
	}

	client, err := ovs.GetOvsConnection("")
	if err != nil {
		res.FailResponse("Failed to connect to OVS", err)
		return
	}
	client.MonitorAll(context.TODO())
	defer client.Disconnect()

	b, err := ovs.GetBridge(client, bridge.Name)
	if err != nil {
		res.FailResponse("Failed to get bridge", err)
		return
	}

	err = ovs.DeleteBridge(client, b)
	if err != nil {
		res.FailResponse("Failed to delete bridge", err)
		return
	}

	res.SuccessResponse("success", nil)
}

// 端口管理
func AddPortHandler(c *gin.Context)    {}
func ListPortsHandler(c *gin.Context)  {}
func GetPortHandler(c *gin.Context)    {}
func UpdatePortHandler(c *gin.Context) {}
func RemovePortHandler(c *gin.Context) {}

// 流表管理
func AddFlowHandler(c *gin.Context)    {}
func ListFlowsHandler(c *gin.Context)  {}
func ClearFlowsHandler(c *gin.Context) {}
