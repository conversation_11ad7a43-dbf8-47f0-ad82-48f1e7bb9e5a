package app

import (
	"hci_agent/internal/app/handler"
	"hci_agent/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORSMiddleware())

	v1 := r.Group("/v1")
	{
		v1.GET("/ping", func(ctx *gin.Context) {
			ctx.JSON(200, gin.H{
				"message": "pong",
			})
		})

		v1.Any("/restart", handler.HandlerRestart)
		v1.Any("/shutdown", handler.HandlerShutdown)
		v1.Any("/maintenance", handler.Maintenance)
		v1.Any("/osd/restart", handler.HandlerOSDRestart)
		v1.POST("/file/upload", handler.HandlerUpload)

		v1.Any("/nic/list", handler.HandlerNic)

		v1.GET("/image/upload", handler.HandlerImageUpload)
		v1.GET("/volume/info", nil)
		v1.GET("/pool/info", nil)
		v1.GET("/image/upload/chunk", handler.HandleChunkUpload)
		v1.GET("/image/upload/complete", handler.HandleComplete)
		v1.GET("/system/info", handler.HandlerCpuInfo)

		// 存储

		//iscsi
		v1.POST("/iscsi/discover", handler.DiscoverTargetsHandler)
		v1.POST("/iscsi/login", handler.LoginTargetHandler)
		v1.POST("/iscsi/discover/login", handler.DiscoverLoginTargetHandler)
		v1.GET("/iscsi/nodes", handler.GetNodesHandler)
		v1.POST("/iscsi/logout", handler.LogoutTargetHandler)
		v1.DELETE("/iscsi/nodes/:targetIQN", handler.DeleteNodeHandler)
		v1.GET("/iscsi/sessions", handler.GetSessionsHandler)
		v1.POST("/iscsi/device/target", handler.GetDeviceTargetHandler)
		v1.POST("/iscsi/sessions/info", handler.GetSessionsInfoHandler)
		

		// OVS路由
		SetupOvsRoutes(v1)

	}

	return r

}

func SetupOvsRoutes(v1 *gin.RouterGroup) {
	// 网桥管理
	v1.POST("/ovs/bridges", handler.CreateBridgeHandler)
	v1.GET("/ovs/bridges", handler.ListBridgesHandler)
	v1.GET("/ovs/bridges/:bridgeName", handler.GetBridgeHandler)
	v1.PUT("/ovs/bridges/:bridgeName", handler.UpdateBridgeHandler)
	v1.DELETE("/ovs/bridges/:bridgeName", handler.DeleteBridgeHandler)

	// 端口管理
	v1.POST("/ovs/bridges/:bridgeName/ports", handler.AddPortHandler)
	v1.GET("/ovs/bridges/:bridgeName/ports", handler.ListPortsHandler)
	v1.GET("/ovs/bridges/:bridgeName/ports/:portName", handler.GetPortHandler)
	v1.PUT("/ovs/bridges/:bridgeName/ports/:portName", handler.UpdatePortHandler)
	v1.DELETE("/ovs/bridges/:bridgeName/ports/:portName", handler.RemovePortHandler)

	// 流表管理
	// v1.POST("/ovs/bridges/:bridgeName/flows", handler.AddFlowHandler)
	// v1.GET("/ovs/bridges/:bridgeName/flows", handler.ListFlowsHandler)
	// v1.DELETE("/ovs/bridges/:bridgeName/flows", handler.ClearFlowsHandler)
}
