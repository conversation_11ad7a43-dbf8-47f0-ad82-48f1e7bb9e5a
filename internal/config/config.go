package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

var Conf = new(Config)

type Config struct {
	App App `mapstructure:"app" yaml:"app"`
	Db  Db  `mapstructure:"database" yaml:"database"`
}

type App struct {
	Host string `mapstructure:"host" yaml:"host"`
	Port string    `mapstructure:"port" yaml:"port"`
}

type Db struct {
	Driver   string `mapstructure:"driver" yaml:"driver"`
	Host     string `mapstructure:"host" yaml:"host"`
	Port     string    `mapstructure:"port" yaml:"port"`
	User     string `mapstructure:"user" yaml:"user"`
	Password string `mapstructure:"password" yaml:"password"`
	Name   string `mapstructure:"name" yaml:"name"`
}


// searchPaths 配置文件搜索路径
var searchPaths = []string{
	"/etc/thestack/conf/hci_agent",
	"./conf",
	"./",
}

// NewPath 添加配置文件搜索路径, 暂时没用
func NewPath(path string) []string {
	searchPaths = append(searchPaths, path)
	return searchPaths
}

// InitConfig 初始化配置文件
// filepath: 文件路径
// filename: 文件名
// filetype: 文件类型
func InitConfig() *viper.Viper {

	filename := "config"
	filetype := "yaml"
	fullfile := filename + "."+ filetype
	filepath, err := FindFile(fullfile)
	fmt.Println(filepath)

	if err != nil {
		log.Fatalf("配置文件路径查询报错：%s", err)
	}

	v := viper.New()
	v.AddConfigPath(filepath) // 路径(当前路径下的conf文件夹)
	v.SetConfigName(filename) // 名称
	v.SetConfigType(filetype) // 类型
	err = v.ReadInConfig()   // 读配置
	if err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Println("找不到配置文件..", err.Error())
		} else {
			fmt.Println("配置文件出错..")
		}
	}
	// [section] 如果没写就是 default,也就是下面的 key 变成 default.username

	err = v.Unmarshal(&Conf)
	if err != nil {
		panic(err)
	}
	//fmt.Println(Conf)
	return v
}


// FindFile 查找配置文件是否在搜索路径中，如果在，返回该路径，否则返回错误。
func FindFile(filename string) (string, error) {
	for _, path := range searchPaths {
		fullPath := filepath.Join(path, filename)
		if _, err := os.Stat(fullPath); err == nil {
			return path, nil
		}
	}
	return "", fmt.Errorf("file %s not found in any of the search paths", filename)
}