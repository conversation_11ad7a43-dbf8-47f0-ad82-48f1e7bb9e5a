package database

import (
	"hci_agent/internal/config"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// GetMySQLDSN 根据配置生成MySQL的DSN
func GetMySQLDSN(cfg config.Db) string {
	return cfg.User + ":" + cfg.Password + "@tcp(" + cfg.Host + ":" + cfg.Port + ")/" + cfg.Name + "?charset=utf8mb4&parseTime=True&loc=Local"
}

// InitDB 初始化数据库连接
func InitDB() (*gorm.DB, error) {

	cfg := config.Conf.Db

	dsn := GetMySQLDSN(cfg)
	log.Println("dsn: ", dsn)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("failed to connect database: %v", err)
		return nil, err
	}

	// 设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("failed to get generic database object from orm: %v", err)
		return nil, err
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Second * 10)

	log.Println("Database connected successfully")
	return db, nil
}
