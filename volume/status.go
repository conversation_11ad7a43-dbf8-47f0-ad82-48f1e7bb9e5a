package volume

import (
	"encoding/json"
	"fmt"
	"hci_agent/pkg/logging"
	"net"
	"strings"
)

var DockerAPIURL = "http://unix/var/run/docker.sock/volumes"

type Volumes struct {
	Name   string `json:"Name"`
	Driver string `json:"Driver"`
}

func GetDockerVolumeStatus() ([]Volumes, []string) {
	// 构建 Unix 套接字路径
	socketPath := "/var/run/docker.sock"

	// 建立与 Docker 守护进程的 Unix 套接字连接
	conn, err := net.Dial("unix", socketPath)
	if err != nil {
		fmt.Println("Error connecting to Docker socket:", err)
		return nil, nil
	}
	defer conn.Close()

	// 构建 Docker API 请求
	request := "GET /volumes HTTP/1.0\r\n\r\n"

	// 发送请求到 Docker 守护进程
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Println("Error sending request:", err)
		return nil, nil
	}

	// 读取 Docker 守护进程的响应
	responseBytes := make([]byte, 8192) // 假设响应不超过 8192 字节
	n, err := conn.Read(responseBytes)
	if err != nil {
		fmt.Println("Error reading response:", err)
		return nil, nil
	}

	// 解析响应数据
	response := string(responseBytes[:n])
	var volumes []Volumes
	var volumesName []string
	// 解析卷列表
	volumesStart := strings.Index(response, `[`)
	volumesEnd := strings.LastIndex(response, `]`)
	if volumesStart != -1 && volumesEnd != -1 {
		volumeListJSON := response[volumesStart : volumesEnd+1]

		if err := json.Unmarshal([]byte(volumeListJSON), &volumes); err != nil {
			logging.Log.Error("Error decoding JSON:", err)
			return nil, nil
		}
		// // 打印卷列表
		for _, volume := range volumes {
			volumesName = append(volumesName, volume.Name)
		}
	}
	return volumes, volumesName
}

func Contains(elems []string, v string) bool {
	for _, s := range elems {
		if v == s {
			return true
		}
	}
	return false
}
