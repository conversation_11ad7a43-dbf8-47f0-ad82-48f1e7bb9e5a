package main

import (
	"hci_agent/alert"
	"hci_agent/disk"
	"hci_agent/model"
	"hci_agent/network"
	"hci_agent/pkg/logging"
	"hci_agent/pkg/setting"
	"hci_agent/pkg/utils"
	"hci_agent/system"
	"hci_agent/upload"
	"hci_agent/volume"
	"log"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
	//Gauge 仪表盘类型
	jobsInDisk = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "thxh_theexport_new_disk_info",
		Help: "thxh_theexport_new_disk_infoo in percentage",
	}, []string{"diskName", "hostName", "diskSize", "externalIp"})
	//Gauge 仪表盘类型
	DockerVolumeStatus = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "thxh_theexport_docker_volume_status",
		Help: "thxh_theexport_docker_volume_status in percentage",
	}, []string{"volumeName", "hostName", "externalIp"})

	networkStatus = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "node_network_status",
			Help: "Value is 1 if operstate is 'up', 0 otherwise.",
		}, []string{"device"},
	)

	Keys   []string
	DRIVER = "tianwen1:5000/the/rbd:latest"
)

func init() {
	setting.Setup()
	prometheus.MustRegister(jobsInDisk)
	prometheus.MustRegister(DockerVolumeStatus)
	prometheus.MustRegister(networkStatus)
	go model.Setup()
	utils.GetLibvirtConn(setting.AppSetting.Host)
}

func main() {
	automated := "false"
	args := os.Args
	if len(args) > 1 {
		automated = args[1]
	}
	logging.SetupZap()
	//shutdownHandler := &system.ShutdownHandler{}
	//restartHandler := &system.RestartHandler{}
	//maintenanceModeHandler := &system.MaintenanceModeHandler{}
	hostName, _ := os.Hostname()
	Keys = make([]string, 0)
	DiskMonitoring := new(disk.Info)
	DiskMonitoring.Hostname = hostName
	ConfKeys := make([]string, 0)
	GlobalKeys := make([]string, 0)
	diskCh := make(chan string, 10)
	//go DiskMonitoring.WatchDisksDocker(diskCh)
	//go DiskMonitoring.WatchDisksDocker(diskCh)
	go DiskMonitoring.GetDiskSignal(&Keys, diskCh)
	exIp := network.GetExternalIP()
	go func() {
		for {
			volumes, volumesName := volume.GetDockerVolumeStatus()
			for _, volumeInfo := range volumes {
				if volumeInfo.Name == "thealertmanager" {
					if volumeInfo.Driver == DRIVER {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(1)
					} else {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(0)
					}
				}
				if volumeInfo.Name == "thedb" {
					if volumeInfo.Driver == DRIVER {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(1)
					} else {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(0)
					}
				}
				if volumeInfo.Name == "theesdata" {
					if volumeInfo.Driver == DRIVER {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(1)
					} else {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(0)
					}
				}
				if volumeInfo.Name == "thegrafana" {
					if volumeInfo.Driver == DRIVER {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(1)
					} else {
						DockerVolumeStatus.WithLabelValues(volumeInfo.Name, hostName, exIp).Set(0)
					}
				}
			}
			if !volume.Contains(volumesName, "thealertmanager") {
				DockerVolumeStatus.DeleteLabelValues("thealertmanager", hostName)
			}
			if !volume.Contains(volumesName, "thedb") {
				DockerVolumeStatus.DeleteLabelValues("thedb", hostName)
			}
			if !volume.Contains(volumesName, "theesdata") {
				DockerVolumeStatus.DeleteLabelValues("theesdata", hostName)
			}
			if !volume.Contains(volumesName, "thegrafana") {
				DockerVolumeStatus.DeleteLabelValues("thegrafana", hostName)
			}
			time.Sleep(30 * time.Second)
		}
	}()
	go collectNetworkMetrics()
	logging.Log.Infof("是否执行自动化操作盘: %s", automated)
	if automated == "true" {
		go func() {
			for {
				for _, key := range Keys {
					if strings.Contains(key, "sd") {
						isok, err := DiskMonitoring.CheckIfCAndCephDrive(key)
						if err != nil {
							log.Println("failed to CheckIfCAndCephDrive")
						}
						if isok {
							GlobalKeys = append(GlobalKeys, key)
							size := DiskMonitoring.GetDiskSize(key)
							jobsInDisk.WithLabelValues(key, hostName, size, exIp).Set(0)
						}
					}
				}
				// ConfKeys 赋值
				ConfKeys = DiskMonitoring.GetConfig()
				adds := DiskMonitoring.Difference(GlobalKeys, ConfKeys)
				for _, add := range adds {
					vg, _ := DiskMonitoring.GetVolumeGroupForDisk(add)
					if strings.Contains(vg, "ceph") {
						continue
					} else {
						if strings.Contains(vg, "cinder") {
							DiskMonitoring.AddConfig(add)
						} else {
							DiskMonitoring.RunShell(add)
							DiskMonitoring.AddConfig(add)
						}
					}
				}
				dels := DiskMonitoring.Difference(ConfKeys, GlobalKeys)
				for _, del := range dels {
					DiskMonitoring.DelConfig(del)
					size := DiskMonitoring.GetDiskSize(del)
					jobsInDisk.DeleteLabelValues(del, hostName, size, exIp)
				}
				Keys = nil
				GlobalKeys = nil
				ConfKeys = nil
				time.Sleep(30 * time.Second)
			}
		}()
	}
	http.Handle("/metrics", promhttp.Handler())
	http.HandleFunc("/restart", system.HandlerRestart)
	http.HandleFunc("/shutdown", system.HandlerShutdown)
	http.HandleFunc("/maintenance", system.HandlerMaintenance)
	http.HandleFunc("/osd/restart", system.HandlerOSDRestart)
	http.HandleFunc("/file/upload", upload.HandlerUpload)
	http.HandleFunc("/nic/list", upload.HandlerNic)
	http.HandleFunc("/nic/by_ovs_bridge", upload.HandlerNicByOvsBridge)
	http.HandleFunc("/image/upload", upload.HandlerImageUpload)
	http.HandleFunc("/volume/info", upload.Volume_info)
	http.HandleFunc("/pool/info", upload.PoolInfo)
	http.HandleFunc("/image/upload/chunk", upload.HandleChunkUpload)
	http.HandleFunc("/image/upload/complete", upload.HandleComplete)
	http.HandleFunc("/alert/rules", alert.HandleSetAlertRules)
	http.HandleFunc("/alert/methods", alert.HandleSetAlertMethods)
	http.HandleFunc("/system/info", system.HandlerCpuInfo)
	//log.Fatal(http.ListenAndServe(*addr, nil))
	log.Fatal(http.ListenAndServe(":9178", nil))
}

func shouldFilterInterface(name string) bool {
	prefixesToFilter := []string{"veth", "br-", "docker", "virbr", "lo", "tap"}
	for _, prefix := range prefixesToFilter {
		if strings.HasPrefix(name, prefix) {
			return true
		}
	}
	return false
}

func collectNetworkMetrics() {
	for {
		interfaces, err := net.Interfaces()
		if err != nil {
			log.Printf("Error getting network interfaces: %v", err)
			continue
		}

		for _, iface := range interfaces {
			if shouldFilterInterface(iface.Name) {
				continue
			}

			status := 0.0
			if iface.Flags&net.FlagUp != 0 {
				status = 1.0
			}

			networkStatus.WithLabelValues(iface.Name).Set(status)
			//fmt.Printf("Interface: %s, Status: %.0f\n", iface.Name, status)
		}

		time.Sleep(30 * time.Second)
	}
}
