######第三版
# build stage
FROM tianwen1:5000/golang:alpine AS builder
WORKDIR /app
ENV GOPROXY=https://goproxy.cn,direct

# 复制go.mod和go.sum文件，下载依赖
COPY go.mod go.sum ./
RUN apk add pkgconfig build-base ncurses-dev libxml2-dev openssl-dev libffi-dev glib-dev zlib-dev gmp-dev gnutls-dev pcre2-dev sqlite-dev libvirt libvirt-dev && go mod tidy

# 复制应用程序代码并编译
COPY . .
RUN go get libvirt.org/go/libvirt && go build -o /go/bin/hci_agent && \
    chmod 777 /go/bin/hci_agent

# final stage
FROM tianwen1:5000/alpine:latest

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS

# 更改Alpine源为阿里云
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk add --no-cache pkgconfig build-base ncurses-dev libxml2-dev openssl-dev libffi-dev glib-dev zlib-dev gmp-dev gnutls-dev pcre2-dev sqlite-dev libvirt libvirt-dev util-linux lvm2 docker busybox bash && \
    mkdir -p /opt/hci
COPY --from=builder /go/bin/hci_agent /opt/hci/
WORKDIR /opt/hci
EXPOSE 9178
ENTRYPOINT ["./hci_agent"]
