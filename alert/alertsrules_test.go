package alert

import (
	"fmt"
	"log"
	"testing"
)

func Test_getAlertRuleGroup(t *testing.T) {
	// 创建测试数据
	testRules := []AlertRule{
		{
			ID:            1,
			Name:          "test_alert",
			Expr:          "up == 0",
			ExprCode:      "0",
			ForInterval:   "5m",
			CriticalValue: -1,
			MajorValue:    0,
			WarningValue:  0,
			InfoValue:     0,
			Description:   "Test alert description",
			Summary:       "Test alert summary",
			Job:           "test_job",
			Status:        "enabled",
		},
	}

	outputFilePath := "rules.yml"

	if err := GetAndSaveRules(testRules, outputFilePath); err != nil {
		log.Fatalf("Failed to get and save rules: %v", err)
	}

	ip := getVip()
	err := reloadPrometheus(ip)
	if err != nil {
		fmt.Printf("Failed to reload Prometheus: %v\n", err)
	} else {
		fmt.Println("Prometheus reloaded successfully.")
	}
}
