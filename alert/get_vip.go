package alert

import (
	"gopkg.in/yaml.v2"
	"log"
	"os"
	"strings"
)

type GlobalConfig struct {
	ScrapeInterval     string `yaml:"scrape_interval"`
	EvaluationInterval string `yaml:"evaluation_interval"`
}

type AlertManager struct {
	Alertmanagers []struct {
		StaticConfigs []struct {
			Targets []string `yaml:"targets"`
		} `yaml:"static_configs"`
	} `yaml:"alertmanagers"`
}

type ScrapeConfig struct {
	JobName        string `yaml:"job_name"`
	ScrapeInterval string `yaml:"scrape_interval"`
	StaticConfigs  []struct {
		Targets []string `yaml:"targets"`
	} `yaml:"static_configs"`
	HttpSDConfigs []struct {
		URL             string `yaml:"url"`
		RefreshInterval string `yaml:"refresh_interval"`
	} `yaml:"http_sd_configs"`
}

type PrometheusConfig struct {
	Global   GlobalConfig   `yaml:"global"`
	Alerting AlertManager   `yaml:"alerting"`
	Rules    []string       `yaml:"rule_files"`
	Scrapes  []ScrapeConfig `yaml:"scrape_configs"`
}

func getVip() string {
	// 读取 prometheus.yml 文件
	//data, err := os.ReadFile("prometheus.yml")
	data, err := os.ReadFile("/etc/thestack/conf/prometheus/prometheus.yml")
	if err != nil {
		log.Printf("Error reading file: %v", err)
		return ""
	}

	var config PrometheusConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		log.Printf("Error unmarshalling YAML: %v", err)
		return ""
	}

	for _, scrapeConfig := range config.Scrapes {
		if scrapeConfig.JobName == "node_exporter" {
			if len(scrapeConfig.HttpSDConfigs) > 0 {
				url := scrapeConfig.HttpSDConfigs[0].URL
				if strings.HasPrefix(url, "http://") {
					urlWithoutProtocol := strings.TrimPrefix(url, "http://")
					parts := strings.Split(urlWithoutProtocol, "/")
					if len(parts) > 0 {
						ipPort := strings.Split(parts[0], ":")
						if len(ipPort) > 0 {
							return ipPort[0]
						}
					}
				}
			}
		}
	}

	return ""
}
