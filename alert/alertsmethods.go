package alert

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"gopkg.in/yaml.v3"
)

// AlertMethod represents an alert method in the database.
type AlertMethod struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment" json:"id"`
	Name        string `gorm:"column:name" json:"name"`
	MethodType  string `gorm:"column:method_type" json:"method_type"`
	Description string `gorm:"column:description" json:"description"`

	// 通用字段
	IsEnabled int    `gorm:"column:is_enabled" json:"is_enabled"`
	IsDefault int    `gorm:"column:is_default" json:"is_default"`
	OrderID   int    `gorm:"column:order_id" json:"order_id"`
	Status    string `gorm:"column:status" json:"status"`

	// 邮件告警字段
	SmtpFrom         string `gorm:"column:smtp_from" json:"smtp_from"`
	SmtpSmarthost    string `gorm:"column:smtp_smarthost" json:"smtp_smarthost"`
	SmtpAuthUsername string `gorm:"column:smtp_auth_username" json:"smtp_auth_username"`
	SmtpAuthPassword string `gorm:"column:smtp_auth_password" json:"smtp_auth_password"`
	SmtpRequireTLS   int    `gorm:"column:smtp_require_tls" json:"smtp_require_tls"`
	EmailTo          string `gorm:"column:email_to" json:"email_to"`
	EmailSubject     string `gorm:"column:email_subject" json:"email_subject"`
	EmailBody        string `gorm:"column:email_body" json:"email_body"`

	// 钉钉告警字段
	DingtalkWebhookURL string `gorm:"column:dingtalk_webhook_url" json:"dingtalk_webhook_url"`
	DingtalkSecret     string `gorm:"column:dingtalk_secret" json:"dingtalk_secret"`
	DingtalkAtMobiles  string `gorm:"column:dingtalk_at_mobiles" json:"dingtalk_at_mobiles"`
	DingtalkAtAll      int    `gorm:"column:dingtalk_at_all" json:"dingtalk_at_all"`
	DingtalkTitle      string `gorm:"column:dingtalk_title" json:"dingtalk_title"`
	DingtalkContent    string `gorm:"column:dingtalk_content" json:"dingtalk_content"`

	// Webhook告警字段
	WebhookURL     string `gorm:"column:webhook_url" json:"webhook_url"`
	WebhookMethod  string `gorm:"column:webhook_method" json:"webhook_method"`
	WebhookHeaders string `gorm:"column:webhook_headers" json:"webhook_headers"`
	WebhookBody    string `gorm:"column:webhook_body" json:"webhook_body"`
	WebhookTimeout int    `gorm:"column:webhook_timeout" json:"webhook_timeout"`

	// 短信告警字段
	SmsAPIURL       string `gorm:"column:sms_api_url" json:"sms_api_url"`
	SmsAPIKey       string `gorm:"column:sms_api_key" json:"sms_api_key"`
	SmsAPISecret    string `gorm:"column:sms_api_secret" json:"sms_api_secret"`
	SmsTemplateID   string `gorm:"column:sms_template_id" json:"sms_template_id"`
	SmsSignName     string `gorm:"column:sms_sign_name" json:"sms_sign_name"`
	SmsPhoneNumbers string `gorm:"column:sms_phone_numbers" json:"sms_phone_numbers"`
	SmsContent      string `gorm:"column:sms_content" json:"sms_content"`

	// 企业微信告警字段
	WechatWebhookURL          string `gorm:"column:wechat_webhook_url" json:"wechat_webhook_url"`
	WechatMentionedList       string `gorm:"column:wechat_mentioned_list" json:"wechat_mentioned_list"`
	WechatMentionedMobileList string `gorm:"column:wechat_mentioned_mobile_list" json:"wechat_mentioned_mobile_list"`
	WechatTitle               string `gorm:"column:wechat_title" json:"wechat_title"`
	WechatContent             string `gorm:"column:wechat_content" json:"wechat_content"`
}

// TableName returns the table name used by AlertMethod.
func (AlertMethod) TableName() string {
	return "alert_methods"
}

// AlertmanagerConfig represents the Alertmanager configuration structure
type AlertmanagerConfig struct {
	Global    AlertmanagerGlobal     `yaml:"global"`
	Route     AlertmanagerRoute      `yaml:"route"`
	Receivers []AlertmanagerReceiver `yaml:"receivers"`
}

// AlertmanagerGlobal represents global configuration
type AlertmanagerGlobal struct {
	SmtpFrom         string `yaml:"smtp_from,omitempty"`
	SmtpSmarthost    string `yaml:"smtp_smarthost,omitempty"`
	SmtpAuthUsername string `yaml:"smtp_auth_username,omitempty"`
	SmtpAuthPassword string `yaml:"smtp_auth_password,omitempty"`
	SmtpRequireTLS   bool   `yaml:"smtp_require_tls,omitempty"`
}

// AlertmanagerRoute represents routing configuration
type AlertmanagerRoute struct {
	GroupBy        []string `yaml:"group_by"`
	GroupWait      string   `yaml:"group_wait"`
	GroupInterval  string   `yaml:"group_interval"`
	RepeatInterval string   `yaml:"repeat_interval"`
	Receiver       string   `yaml:"receiver"`
}

// AlertmanagerReceiver represents a receiver configuration
type AlertmanagerReceiver struct {
	Name           string                      `yaml:"name"`
	EmailConfigs   []AlertmanagerEmailConfig   `yaml:"email_configs,omitempty"`
	WebhookConfigs []AlertmanagerWebhookConfig `yaml:"webhook_configs,omitempty"`
}

// AlertmanagerEmailConfig represents email configuration
type AlertmanagerEmailConfig struct {
	To      string `yaml:"to"`
	Subject string `yaml:"subject,omitempty"`
	Body    string `yaml:"body,omitempty"`
	HTML    string `yaml:"html,omitempty"`
}

// AlertmanagerWebhookConfig represents webhook configuration
type AlertmanagerWebhookConfig struct {
	URL          string                 `yaml:"url"`
	SendResolved bool                   `yaml:"send_resolved,omitempty"`
	HTTPConfig   map[string]interface{} `yaml:"http_config,omitempty"`
}

// GetAndSaveAlertmanagerConfig processes alert methods and saves them as an Alertmanager config file.
func GetAndSaveAlertmanagerConfig(methods []AlertMethod, outputFilePath string) error {
	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		return fmt.Errorf("failed to generate alertmanager config: %w", err)
	}

	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to convert alertmanager config to YAML: %w", err)
	}

	err = saveYAMLToFile(yamlBytes, outputFilePath)
	if err != nil {
		return fmt.Errorf("failed to write YAML to file: %w", err)
	}

	log.Printf("Alertmanager config has been saved to '%s'\n", outputFilePath)
	return nil
}

// generateAlertmanagerConfig generates Alertmanager configuration from alert methods
func generateAlertmanagerConfig(methods []AlertMethod) (*AlertmanagerConfig, error) {
	config := &AlertmanagerConfig{
		Global: AlertmanagerGlobal{},
		Route: AlertmanagerRoute{
			GroupBy:        []string{"alertname"},
			GroupWait:      "10s",
			GroupInterval:  "10s",
			RepeatInterval: "1h",
			Receiver:       "default",
		},
		Receivers: []AlertmanagerReceiver{},
	}

	// 添加默认接收器（总是添加，确保路由有效）
	defaultReceiver := AlertmanagerReceiver{
		Name: "default",
	}
	config.Receivers = append(config.Receivers, defaultReceiver)

	// 处理启用的告警方式
	for _, method := range methods {
		if method.IsEnabled != 1 {
			continue
		}

		receiver := AlertmanagerReceiver{
			Name: method.Name,
		}

		switch method.MethodType {
		case "email":
			if method.SmtpFrom != "" && config.Global.SmtpFrom == "" {
				config.Global.SmtpFrom = method.SmtpFrom
				config.Global.SmtpSmarthost = method.SmtpSmarthost
				config.Global.SmtpAuthUsername = method.SmtpAuthUsername
				config.Global.SmtpAuthPassword = method.SmtpAuthPassword
				config.Global.SmtpRequireTLS = method.SmtpRequireTLS == 1
			}

			if method.EmailTo != "" {
				emailConfig := AlertmanagerEmailConfig{
					To:      method.EmailTo,
					Subject: method.EmailSubject,
					Body:    method.EmailBody,
				}
				receiver.EmailConfigs = append(receiver.EmailConfigs, emailConfig)
			}

		case "dingtalk", "webhook":
			var webhookURL string
			if method.MethodType == "dingtalk" {
				webhookURL = method.DingtalkWebhookURL
			} else {
				webhookURL = method.WebhookURL
			}

			if webhookURL != "" {
				webhookConfig := AlertmanagerWebhookConfig{
					URL:          webhookURL,
					SendResolved: true,
				}
				receiver.WebhookConfigs = append(receiver.WebhookConfigs, webhookConfig)
			}
		}

		if len(receiver.EmailConfigs) > 0 || len(receiver.WebhookConfigs) > 0 {
			config.Receivers = append(config.Receivers, receiver)
		}
	}

	return config, nil
}

// reloadAlertmanager sends a reload signal to Alertmanager
func reloadAlertmanager(ip string) error {
	url := fmt.Sprintf("http://%s:9093/-/reload", ip)
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status code %d and message: %s", resp.StatusCode, string(body))
	}

	return nil
}

func SetAlertMethods(methods []AlertMethod) {
	ip := getVip()
	outputFilePath := "/etc/thestack/conf/alertmanager/alertmanager.yml"

	if err := GetAndSaveAlertmanagerConfig(methods, outputFilePath); err != nil {
		log.Printf("Failed to get and save alertmanager config: %v", err)
		return
	}

	err := reloadAlertmanager(ip)
	if err != nil {
		fmt.Printf("Failed to reload Alertmanager: %v\n", err)
		return
	} else {
		fmt.Println("Alertmanager reloaded successfully.")
	}
}

func HandleSetAlertMethods(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头部
	w.Header().Set("Access-Control-Allow-Origin", "*") // 允许所有来源，或指定具体的来源
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		// 预检请求直接返回200状态码
		w.WriteHeader(http.StatusOK)
		return
	}

	// 如果不是POST请求，则返回405 Method Not Allowed
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析body体数据
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 解析JSON数据到AlertMethod数组
	var methods []AlertMethod
	if err := json.Unmarshal(body, &methods); err != nil {
		http.Error(w, "Failed to parse JSON: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 调用SetAlertMethods函数处理规则
	SetAlertMethods(methods)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	response := map[string]string{
		"status":  "success",
		"message": "Alert methods have been updated successfully",
	}
	json.NewEncoder(w).Encode(response)
}
