package alert

import (
	"encoding/json"
	"fmt"
	"gopkg.in/yaml.v3"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
)

// AlertRule represents an alert rule in the database.
type AlertRule struct {
	ID       int    `gorm:"column:id;primary_key;auto_increment" json:"id"` // int(11) NOT NULL AUTO_INCREMENT
	Name     string `gorm:"column:name" json:"name"`                        // varchar(255) NOT NULL
	Expr     string `gorm:"column:expr" json:"expr"`                        // varchar(512) NOT NULL
	ExprCode string `gorm:"column:expr_code" json:"expr_code"`              // varchar(32) DEFAULT NULL
	//Value          int       `gorm:"column:value" json:"value"`                      // int(11) DEFAULT NULL
	Unit           string `gorm:"column:unit" json:"unit"`                       // varchar(16) DEFAULT NULL
	ForInterval    string `gorm:"column:for_interval" json:"for_interval"`       // varchar(20) NOT NULL
	ReportInterval string `gorm:"column:report_interval" json:"report_interval"` // varchar(20) DEFAULT NULL
	CriticalValue  int    `gorm:"column:critical_value" json:"critical_value"`   // varchar(50) DEFAULT NULL
	MajorValue     int    `gorm:"column:major_value" json:"major_value"`         // varchar(50) DEFAULT NULL
	WarningValue   int    `gorm:"column:warning_value" json:"warning_value"`     // varchar(50) DEFAULT NULL
	InfoValue      int    `gorm:"column:info_value" json:"info_value"`           // varchar(50) DEFAULT NULL
	Description    string `gorm:"column:description" json:"description"`         // varchar(255) DEFAULT NULL
	Summary        string `gorm:"column:summary" json:"summary"`                 // varchar(255) DEFAULT NULL
	Job            string `gorm:"column:job" json:"job"`                         // varchar(255) DEFAULT NULL
	AlertType      string `gorm:"column:alert_type" json:"alert_type"`           // varchar(64) DEFAULT NULL
	// 忽略时间字段，因为我们不需要使用它们
	// CreatedAt      *time.Time `gorm:"column:created_at" json:"created_at"`           // datetime DEFAULT current_timestamp()
	// UpdatedAt      *time.Time `gorm:"column:updated_at" json:"updated_at"`           // datetime DEFAULT current_timestamp()
	IsDefault int    `gorm:"column:is_default" json:"is_default"` // int(11) DEFAULT NULL
	Status    string `gorm:"column:status" json:"status"`         // varchar(50) DEFAULT NULL
	OrderID   int    `gorm:"column:order_id" json:"order_id"`     // int(11) DEFAULT NULL
}

// TableName returns the table name used by AlertRule.
func (AlertRule) TableName() string {
	return "alert_rules"
}

// AlertRuleGroups groups multiple alert rules.
type AlertRuleGroups struct {
	ID    int    `gorm:"primaryKey"`
	Rules string `gorm:"rules"`
}

// PrometheusRule represents a single Prometheus rule.
type PrometheusRule struct {
	Alert       string            `json:"alert" yaml:"alert"`
	Expr        string            `json:"expr" yaml:"expr"`
	For         string            `json:"for" yaml:"for"`
	Labels      map[string]string `json:"labels" yaml:"labels"`
	Annotations map[string]string `json:"annotations" yaml:"annotations"`
}

// PrometheusRuleGroup represents a group of Prometheus rules.
type PrometheusRuleGroup struct {
	Name  string           `json:"name" yaml:"name"`
	Rules []PrometheusRule `json:"rules" yaml:"rules"`
}

// PrometheusRules is the top-level structure for Prometheus rules.
type PrometheusRules struct {
	Groups []PrometheusRuleGroup `json:"groups" yaml:"groups"`
}

// GetAndSaveRules processes alert rules and saves them as a Prometheus rules file.
func GetAndSaveRules(rules []AlertRule, outputFilePath string) error {
	group, err := getAlertRuleGroup(rules)
	if err != nil {
		return fmt.Errorf("failed to process alert rules: %w", err)
	}

	prometheusRules := &PrometheusRules{
		Groups: []PrometheusRuleGroup{*group},
	}

	yamlBytes, err := marshalRulesToYAML(prometheusRules)
	if err != nil {
		return fmt.Errorf("failed to convert alert rules to YAML: %w", err)
	}

	err = saveYAMLToFile(yamlBytes, outputFilePath)
	if err != nil {
		return fmt.Errorf("failed to write YAML to file: %w", err)
	}

	log.Printf("Prometheus rules have been saved to '%s'\n", outputFilePath)
	return nil
}

// getAlertRuleGroup processes a list of alert rules and converts them to a Prometheus rule group.
func getAlertRuleGroup(rules []AlertRule) (*PrometheusRuleGroup, error) {

	prometheusRules := make([]PrometheusRule, 0)

	for _, rule := range rules {
		severityLevels := []struct {
			Severity string
			Value    int
		}{
			{"critical", rule.CriticalValue},
			{"major", rule.MajorValue},
			{"warning", rule.WarningValue},
			{"info", rule.InfoValue},
		}
		for _, level := range severityLevels {
			if level.Value != 0 {
				if rule.ExprCode == "0" {
					if level.Value == -1 {
						PrometheusRuleTemp := PrometheusRule{
							Alert: rule.Name,
							Expr:  rule.Expr,
							For:   rule.ForInterval,
							Labels: map[string]string{
								"severity": level.Severity,
								"job":      rule.Job,
							},
							Annotations: map[string]string{
								"summary":     rule.Summary,
								"description": rule.Description,
							},
						}

						if rule.Job == "" {
							delete(PrometheusRuleTemp.Labels, "job")
						}
						prometheusRules = append(prometheusRules, PrometheusRuleTemp)
						continue
					}

				}
				if rule.ExprCode == "1" {
					if level.Value == 0 {
						continue
					}
					// 将rule.Expr >或 < 后的数字替换为rule.Value
					lastSpaceIndex := strings.LastIndex(rule.Expr, " ")

					// Check if there is a space.
					if lastSpaceIndex != -1 {
						// Get the substring starting from the last space.
						substr := rule.Expr[lastSpaceIndex+1:]

						// Convert the substring to an integer if it's a valid number.
						_, err := strconv.Atoi(substr)
						if err == nil {
							// Replace the old value with the new value.
							modifiedExpr := rule.Expr[:lastSpaceIndex+1] + fmt.Sprintf("%d", level.Value)
							rule.Expr = modifiedExpr
						}
					}
				}
				if rule.ExprCode == "2" {
					if level.Value == 0 {
						continue
					}
					// 将rule.Expr >或 < 后的数字替换为rule.Value
					firstOpIndex := -1

					// 查找第一个比较操作符的位置
					for i, char := range rule.Expr {
						if char == '<' || char == '>' {
							firstOpIndex = i
							break
						}
					}

					// 检查是否找到了比较操作符
					if firstOpIndex != -1 {
						// 获取比较操作符后面的数字及其后续内容
						substr := rule.Expr[firstOpIndex+1:]
						firstDigitIndex := -1
						endDigitIndex := -1

						// 找到第一个数字的位置
						for i, char := range substr {
							if char >= '0' && char <= '9' {
								if firstDigitIndex == -1 {
									firstDigitIndex = i
								}
								endDigitIndex = i + 1
							} else if firstDigitIndex != -1 {
								break
							}
						}

						// 如果找到了数字
						if firstDigitIndex != -1 && endDigitIndex != -1 {
							// 替换旧的数字为新的数字
							modifiedExpr := rule.Expr[:firstOpIndex+1] + fmt.Sprintf("%d", level.Value) + substr[endDigitIndex:]
							rule.Expr = modifiedExpr
						}
					}
				}
				PrometheusRuleTemp := PrometheusRule{
					Alert: rule.Name,
					Expr:  rule.Expr,
					For:   rule.ForInterval,
					Labels: map[string]string{
						"severity": level.Severity,
						"job":      rule.Job,
					},
					Annotations: map[string]string{
						"summary":     rule.Summary,
						"description": rule.Description,
					},
				}

				if rule.Job == "" {
					delete(PrometheusRuleTemp.Labels, "job")
				}
				prometheusRules = append(prometheusRules, PrometheusRuleTemp)
			}
		}
	}

	return &PrometheusRuleGroup{
		Name:  "all_alert",
		Rules: prometheusRules,
	}, nil
}

// parseRuleIDs converts a comma-separated string of rule IDs into an integer array.
func parseRuleIDs(rulesStr string) ([]int, error) {
	ruleIDs := strings.Split(rulesStr, ",")
	intArray := make([]int, len(ruleIDs))

	for i, idStr := range ruleIDs {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return nil, fmt.Errorf("failed to convert ID to integer: %w", err)
		}
		intArray[i] = id
	}

	return intArray, nil
}

// marshalRulesToYAML marshals Prometheus rules to YAML.
func marshalRulesToYAML(rules *PrometheusRules) ([]byte, error) {
	yamlBytes, err := yaml.Marshal(rules)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal rules to YAML: %w", err)
	}
	return yamlBytes, nil
}

// saveYAMLToFile saves YAML data to a file.
func saveYAMLToFile(data []byte, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	_, err = file.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write data to file: %w", err)
	}

	return nil
}

func reloadPrometheus(ip string) error {
	url := fmt.Sprintf("http://%s:9090/-/reload", ip)
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status code %d and message: %s", resp.StatusCode, string(body))
	}

	return nil
}

func SetAlertRules(rules []AlertRule) {
	ip := getVip()
	outputFilePath := "/etc/thestack/conf/prometheus/rules/rules.yml"

	if err := GetAndSaveRules(rules, outputFilePath); err != nil {
		log.Printf("Failed to get and save rules: %v", err)
		return
	}
	err := reloadPrometheus(ip)
	if err != nil {
		fmt.Printf("Failed to reload Prometheus: %v\n", err)
		return
	} else {
		fmt.Println("Prometheus reloaded successfully.")
	}
}

func HandleSetAlertRules(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头部
	w.Header().Set("Access-Control-Allow-Origin", "*") // 允许所有来源，或指定具体的来源
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == http.MethodOptions {
		// 预检请求直接返回200状态码
		w.WriteHeader(http.StatusOK)
		return
	}

	// 如果不是POST请求，则返回405 Method Not Allowed
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析body体数据
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 解析JSON数据到AlertRule数组
	var rules []AlertRule
	if err := json.Unmarshal(body, &rules); err != nil {
		http.Error(w, "Failed to parse JSON: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 调用SetAlertRules函数处理规则
	SetAlertRules(rules)

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	response := map[string]string{
		"status":  "success",
		"message": "Alert rules have been updated successfully",
	}
	json.NewEncoder(w).Encode(response)
}
