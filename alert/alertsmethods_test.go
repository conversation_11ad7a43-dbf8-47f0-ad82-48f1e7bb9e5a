package alert

import (
	"gopkg.in/yaml.v3"
	"testing"
)

func TestGenerateAlertmanagerConfig(t *testing.T) {
	// 测试数据
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "email-alert",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.example.com:587",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "password",
			SmtpRequireTLS:   1,
			EmailTo:          "<EMAIL>",
			EmailSubject:     "Alert: {{ .GroupLabels.alertname }}",
			EmailBody:        "Alert details: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
		{
			ID:                 2,
			Name:               "dingtalk-alert",
			MethodType:         "dingtalk",
			IsEnabled:          1,
			DingtalkWebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=xxx",
			DingtalkSecret:     "secret",
			DingtalkTitle:      "告警通知",
			DingtalkContent:    "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
		{
			ID:            3,
			Name:          "webhook-alert",
			MethodType:    "webhook",
			IsEnabled:     1,
			WebhookURL:    "http://example.com/webhook",
			WebhookMethod: "POST",
		},
		{
			ID:         4,
			Name:       "disabled-alert",
			MethodType: "email",
			IsEnabled:  0, // 禁用的告警方式
			EmailTo:    "<EMAIL>",
		},
	}

	// 生成配置
	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证全局配置
	if config.Global.SmtpFrom != "<EMAIL>" {
		t.Errorf("Expected smtp_from to be '<EMAIL>', got '%s'", config.Global.SmtpFrom)
	}

	if config.Global.SmtpSmarthost != "smtp.example.com:587" {
		t.Errorf("Expected smtp_smarthost to be 'smtp.example.com:587', got '%s'", config.Global.SmtpSmarthost)
	}

	// 验证路由配置
	if config.Route.Receiver != "default" {
		t.Errorf("Expected default receiver to be 'default', got '%s'", config.Route.Receiver)
	}

	// 验证接收器数量（应该有3个启用的接收器）
	if len(config.Receivers) != 3 {
		t.Errorf("Expected 3 receivers, got %d", len(config.Receivers))
	}

	// 验证邮件接收器
	var emailReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "email-alert" {
			emailReceiver = &config.Receivers[i]
			break
		}
	}

	if emailReceiver == nil {
		t.Fatal("Email receiver not found")
	}

	if len(emailReceiver.EmailConfigs) != 1 {
		t.Errorf("Expected 1 email config, got %d", len(emailReceiver.EmailConfigs))
	}

	if emailReceiver.EmailConfigs[0].To != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got '%s'", emailReceiver.EmailConfigs[0].To)
	}

	// 验证钉钉接收器
	var dingtalkReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "dingtalk-alert" {
			dingtalkReceiver = &config.Receivers[i]
			break
		}
	}

	if dingtalkReceiver == nil {
		t.Fatal("Dingtalk receiver not found")
	}

	if len(dingtalkReceiver.WebhookConfigs) != 1 {
		t.Errorf("Expected 1 webhook config for dingtalk, got %d", len(dingtalkReceiver.WebhookConfigs))
	}

	if dingtalkReceiver.WebhookConfigs[0].URL != "https://oapi.dingtalk.com/robot/send?access_token=xxx" {
		t.Errorf("Expected dingtalk webhook URL to match, got '%s'", dingtalkReceiver.WebhookConfigs[0].URL)
	}

	// 测试YAML序列化
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	// 验证YAML输出不为空
	if len(yamlBytes) == 0 {
		t.Error("Generated YAML is empty")
	}

	t.Logf("Generated Alertmanager config:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigEmpty(t *testing.T) {
	// 测试空的告警方式列表
	methods := []AlertMethod{}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 应该有一个默认接收器
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 default receiver, got %d", len(config.Receivers))
	}

	if config.Receivers[0].Name != "default" {
		t.Errorf("Expected default receiver name to be 'default', got '%s'", config.Receivers[0].Name)
	}
}

func TestGenerateAlertmanagerConfigDisabledOnly(t *testing.T) {
	// 测试只有禁用的告警方式
	methods := []AlertMethod{
		{
			ID:         1,
			Name:       "disabled-email",
			MethodType: "email",
			IsEnabled:  0,
			EmailTo:    "<EMAIL>",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 应该只有一个默认接收器
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 default receiver, got %d", len(config.Receivers))
	}

	if config.Receivers[0].Name != "default" {
		t.Errorf("Expected default receiver name to be 'default', got '%s'", config.Receivers[0].Name)
	}
}
