package alert

import (
	"strings"
	"testing"

	"gopkg.in/yaml.v3"
)

func TestGenerateAlertmanagerConfig(t *testing.T) {
	// 测试数据
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "email-alert",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.example.com:587",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "password",
			SmtpRequireTLS:   1,
			EmailTo:          "<EMAIL>",
			EmailSubject:     "Alert: {{ .GroupLabels.alertname }}",
			EmailBody:        "Alert details: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
		{
			ID:                 2,
			Name:               "dingtalk-alert",
			MethodType:         "dingtalk",
			IsEnabled:          1,
			DingtalkWebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=xxx",
			DingtalkSecret:     "secret",
			DingtalkTitle:      "告警通知",
			Dingt<PERSON>Content:    "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
		{
			ID:            3,
			Name:          "webhook-alert",
			MethodType:    "webhook",
			IsEnabled:     1,
			WebhookURL:    "http://example.com/webhook",
			WebhookMethod: "POST",
		},
		{
			ID:         4,
			Name:       "disabled-alert",
			MethodType: "email",
			IsEnabled:  0, // 禁用的告警方式
			EmailTo:    "<EMAIL>",
		},
	}

	// 生成配置
	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证全局配置
	if config.Global.SmtpFrom != "<EMAIL>" {
		t.Errorf("Expected smtp_from to be '<EMAIL>', got '%s'", config.Global.SmtpFrom)
	}

	if config.Global.SmtpSmarthost != "smtp.example.com:587" {
		t.Errorf("Expected smtp_smarthost to be 'smtp.example.com:587', got '%s'", config.Global.SmtpSmarthost)
	}

	// 验证路由配置（多个接收器时使用子路由）
	if config.Route.Receiver != "web.hook" {
		t.Errorf("Expected route receiver to be 'web.hook', got '%s'", config.Route.Receiver)
	}

	// 验证接收器数量（应该有1个默认 + 3个启用的接收器）
	if len(config.Receivers) != 4 {
		t.Errorf("Expected 4 receivers (1 default + 3 enabled), got %d", len(config.Receivers))
	}

	// 验证子路由数量
	if len(config.Route.Routes) != 3 {
		t.Errorf("Expected 3 sub-routes, got %d", len(config.Route.Routes))
	}

	// 验证邮件接收器
	var emailReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "email-alert" {
			emailReceiver = &config.Receivers[i]
			break
		}
	}

	if emailReceiver == nil {
		t.Fatal("Email receiver not found")
	}

	if len(emailReceiver.EmailConfigs) != 1 {
		t.Errorf("Expected 1 email config, got %d", len(emailReceiver.EmailConfigs))
	}

	if emailReceiver.EmailConfigs[0].To != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got '%s'", emailReceiver.EmailConfigs[0].To)
	}

	// 验证钉钉接收器
	var dingtalkReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "dingtalk-alert" {
			dingtalkReceiver = &config.Receivers[i]
			break
		}
	}

	if dingtalkReceiver == nil {
		t.Fatal("Dingtalk receiver not found")
	}

	if len(dingtalkReceiver.WebhookConfigs) != 1 {
		t.Errorf("Expected 1 webhook config for dingtalk, got %d", len(dingtalkReceiver.WebhookConfigs))
	}

	if dingtalkReceiver.WebhookConfigs[0].URL != "https://oapi.dingtalk.com/robot/send?access_token=xxx" {
		t.Errorf("Expected dingtalk webhook URL to match, got '%s'", dingtalkReceiver.WebhookConfigs[0].URL)
	}

	// 测试YAML序列化
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	// 验证YAML输出不为空
	if len(yamlBytes) == 0 {
		t.Error("Generated YAML is empty")
	}

	t.Logf("Generated Alertmanager config:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigEmpty(t *testing.T) {
	// 测试空的告警方式列表
	methods := []AlertMethod{}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 应该有一个默认接收器
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 default receiver, got %d", len(config.Receivers))
	}

	if config.Receivers[0].Name != "web.hook" {
		t.Errorf("Expected default receiver name to be 'web.hook', got '%s'", config.Receivers[0].Name)
	}
}

func TestGenerateAlertmanagerConfigDisabledOnly(t *testing.T) {
	// 测试只有禁用的告警方式
	methods := []AlertMethod{
		{
			ID:         1,
			Name:       "disabled-email",
			MethodType: "email",
			IsEnabled:  0,
			EmailTo:    "<EMAIL>",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 应该只有一个默认接收器
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 default receiver, got %d", len(config.Receivers))
	}

	if config.Receivers[0].Name != "web.hook" {
		t.Errorf("Expected default receiver name to be 'web.hook', got '%s'", config.Receivers[0].Name)
	}
}

func TestGenerateAlertmanagerConfigAlwaysHasDefaultReceiver(t *testing.T) {
	// 测试确保总是有默认接收器，避免"undefined receiver default"错误
	methods := []AlertMethod{
		{
			ID:            1,
			Name:          "test-email",
			MethodType:    "email",
			IsEnabled:     1,
			SmtpFrom:      "<EMAIL>",
			SmtpSmarthost: "smtp.example.com:587",
			EmailTo:       "<EMAIL>",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证路由指向第一个有效接收器
	if config.Route.Receiver != "test-email" {
		t.Errorf("Route receiver should be 'test-email', got '%s'", config.Route.Receiver)
	}

	// 验证配置有效（应该有1个邮件接收器）
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 receiver (email), got %d", len(config.Receivers))
	}

	// 验证接收器名称正确
	if config.Receivers[0].Name != "test-email" {
		t.Errorf("Expected receiver name to be 'test-email', got '%s'", config.Receivers[0].Name)
	}

	// 生成YAML验证
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	t.Logf("Generated config with default receiver:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigQQEmail(t *testing.T) {
	// 测试QQ邮箱配置（465端口SSL）
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "qq-email",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.qq.com:465",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "esujvcvbrgtqbfcd",
			SmtpRequireTLS:   1, // 虽然设置为1，但465端口应该使用SSL而不是STARTTLS
			EmailTo:          "<EMAIL>",
			EmailSubject:     "", // 测试默认主题
			EmailBody:        "", // 测试默认内容
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证SMTP配置
	if config.Global.SmtpFrom != "<EMAIL>" {
		t.Errorf("Expected smtp_from to be '<EMAIL>', got '%s'", config.Global.SmtpFrom)
	}

	if config.Global.SmtpSmarthost != "smtp.qq.com:465" {
		t.Errorf("Expected smtp_smarthost to be 'smtp.qq.com:465', got '%s'", config.Global.SmtpSmarthost)
	}

	// 验证465端口不使用STARTTLS
	if config.Global.SmtpRequireTLS {
		t.Error("Port 465 should not use STARTTLS (smtp_require_tls should be false)")
	}

	// 验证邮件配置
	var emailReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "qq-email" {
			emailReceiver = &config.Receivers[i]
			break
		}
	}

	if emailReceiver == nil {
		t.Fatal("QQ email receiver not found")
	}

	if len(emailReceiver.EmailConfigs) != 1 {
		t.Errorf("Expected 1 email config, got %d", len(emailReceiver.EmailConfigs))
	}

	emailConfig := emailReceiver.EmailConfigs[0]
	if emailConfig.To != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got '%s'", emailConfig.To)
	}

	// 验证默认主题和内容
	if emailConfig.Subject == "" {
		t.Error("Email subject should have default value")
	}

	if emailConfig.HTML == "" {
		t.Error("Email HTML content should have default value")
	}

	// 生成YAML验证
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	t.Logf("Generated QQ email config:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigEmailAndDingtalk(t *testing.T) {
	// 测试邮件 + 钉钉两个接收器的配置
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "email-receiver",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.qq.com:465",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "esujvcvbrgtqbfcd",
			SmtpRequireTLS:   1,
			EmailTo:          "<EMAIL>",
			EmailSubject:     "系统告警通知",
			EmailBody:        "告警内容：{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
		{
			ID:                 2,
			Name:               "dingtalk-receiver",
			MethodType:         "dingtalk",
			IsEnabled:          1,
			DingtalkWebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=your_token_here",
			DingtalkSecret:     "your_secret_here",
			DingtalkAtMobiles:  "13800138000,13900139000",
			DingtalkAtAll:      0,
			DingtalkTitle:      "生产环境告警",
			DingtalkContent:    "告警详情：{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证SMTP配置
	if config.Global.SmtpFrom != "<EMAIL>" {
		t.Errorf("Expected smtp_from to be '<EMAIL>', got '%s'", config.Global.SmtpFrom)
	}

	// 验证路由配置（多个接收器时使用子路由）
	if config.Route.Receiver != "web.hook" {
		t.Errorf("Expected route receiver to be 'web.hook', got '%s'", config.Route.Receiver)
	}

	// 验证接收器数量（1个默认 + 2个实际接收器）
	if len(config.Receivers) != 3 {
		t.Errorf("Expected 3 receivers (1 default + 2 actual), got %d", len(config.Receivers))
	}

	// 验证子路由数量
	if len(config.Route.Routes) != 2 {
		t.Errorf("Expected 2 sub-routes, got %d", len(config.Route.Routes))
	}

	// 验证邮件接收器
	var emailReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "email-receiver" {
			emailReceiver = &config.Receivers[i]
			break
		}
	}

	if emailReceiver == nil {
		t.Fatal("Email receiver not found")
	}

	if len(emailReceiver.EmailConfigs) != 1 {
		t.Errorf("Expected 1 email config, got %d", len(emailReceiver.EmailConfigs))
	}

	// 验证钉钉接收器
	var dingtalkReceiver *AlertmanagerReceiver
	for i := range config.Receivers {
		if config.Receivers[i].Name == "dingtalk-receiver" {
			dingtalkReceiver = &config.Receivers[i]
			break
		}
	}

	if dingtalkReceiver == nil {
		t.Fatal("Dingtalk receiver not found")
	}

	if len(dingtalkReceiver.WebhookConfigs) != 1 {
		t.Errorf("Expected 1 webhook config for dingtalk, got %d", len(dingtalkReceiver.WebhookConfigs))
	}

	// 生成YAML查看完整配置
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	t.Logf("Generated config with email + dingtalk receivers:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigMultipleReceiversWithRoutes(t *testing.T) {
	// 测试多接收器路由配置（同时发送到邮件和钉钉）
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "email-alert",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.example.com:587",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "password",
			SmtpRequireTLS:   1,
			EmailTo:          "<EMAIL>",
			EmailSubject:     "邮件告警",
			EmailBody:        "邮件告警内容",
		},
		{
			ID:                 2,
			Name:               "dingtalk-alert",
			MethodType:         "dingtalk",
			IsEnabled:          1,
			DingtalkWebhookURL: "https://oapi.dingtalk.com/robot/send?access_token=test",
			DingtalkTitle:      "钉钉告警",
			DingtalkContent:    "钉钉告警内容",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证接收器数量（应该有3个：1个默认 + 2个实际接收器）
	if len(config.Receivers) != 3 {
		t.Errorf("Expected 3 receivers (1 default + 2 actual), got %d", len(config.Receivers))
	}

	// 验证主路由指向默认接收器
	if config.Route.Receiver != "web.hook" {
		t.Errorf("Expected main route receiver to be 'web.hook', got '%s'", config.Route.Receiver)
	}

	// 验证子路由数量
	if len(config.Route.Routes) != 2 {
		t.Errorf("Expected 2 sub-routes, got %d", len(config.Route.Routes))
	}

	// 验证子路由配置
	expectedReceivers := []string{"email-alert", "dingtalk-alert"}
	for i, subRoute := range config.Route.Routes {
		if subRoute.Receiver != expectedReceivers[i] {
			t.Errorf("Expected sub-route %d receiver to be '%s', got '%s'", i, expectedReceivers[i], subRoute.Receiver)
		}
		if !subRoute.Continue {
			t.Errorf("Sub-route %d should have continue=true", i)
		}
	}

	// 生成YAML查看完整配置
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	t.Logf("Generated config with multiple receivers and sub-routes:\n%s", string(yamlBytes))
}

func TestGenerateAlertmanagerConfigEmailFieldsCorrect(t *testing.T) {
	// 测试邮件配置字段是否正确（html而不是body）
	methods := []AlertMethod{
		{
			ID:               1,
			Name:             "test-email",
			MethodType:       "email",
			IsEnabled:        1,
			SmtpFrom:         "<EMAIL>",
			SmtpSmarthost:    "smtp.example.com:587",
			SmtpAuthUsername: "<EMAIL>",
			SmtpAuthPassword: "password",
			SmtpRequireTLS:   1,
			EmailTo:          "<EMAIL>",
			EmailSubject:     "测试告警",
			EmailBody:        "这是测试内容",
		},
	}

	config, err := generateAlertmanagerConfig(methods)
	if err != nil {
		t.Fatalf("Failed to generate alertmanager config: %v", err)
	}

	// 验证邮件接收器
	if len(config.Receivers) != 1 {
		t.Errorf("Expected 1 receiver, got %d", len(config.Receivers))
	}

	receiver := config.Receivers[0]
	if len(receiver.EmailConfigs) != 1 {
		t.Errorf("Expected 1 email config, got %d", len(receiver.EmailConfigs))
	}

	emailConfig := receiver.EmailConfigs[0]

	// 验证字段存在且正确
	if emailConfig.To != "<EMAIL>" {
		t.Errorf("Expected to='<EMAIL>', got '%s'", emailConfig.To)
	}

	if emailConfig.Subject != "测试告警" {
		t.Errorf("Expected subject='测试告警', got '%s'", emailConfig.Subject)
	}

	if emailConfig.HTML != "这是测试内容" {
		t.Errorf("Expected html='这是测试内容', got '%s'", emailConfig.HTML)
	}

	// 验证没有使用已废弃的body字段
	if emailConfig.Text != "" {
		t.Errorf("Text field should be empty, got '%s'", emailConfig.Text)
	}

	// 生成YAML验证格式
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config to YAML: %v", err)
	}

	yamlStr := string(yamlBytes)

	// 验证YAML中使用的是html字段而不是body字段
	if strings.Contains(yamlStr, "body:") {
		t.Error("Generated YAML should not contain 'body:' field")
	}

	if !strings.Contains(yamlStr, "html:") {
		t.Error("Generated YAML should contain 'html:' field")
	}

	t.Logf("Generated config with correct email fields:\n%s", yamlStr)
}
