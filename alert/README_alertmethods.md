# Alertmanager配置更新和热更新功能

## 功能概述

本功能实现了根据数据库中的告警方式配置自动生成Alertmanager配置文件并进行热更新的功能。

## 主要特性

1. **支持多种告警方式**：
   - 邮件告警 (email)
   - 钉钉告警 (dingtalk)
   - Webhook告警 (webhook)
   - 短信告警 (sms) - 通过webhook实现
   - 企业微信告警 (wechat) - 通过webhook实现

2. **自动配置生成**：根据数据库中的告警方式配置自动生成标准的Alertmanager YAML配置文件

3. **热更新支持**：配置更新后自动调用Alertmanager的reload API进行热更新

4. **灵活的配置管理**：支持启用/禁用告警方式，支持默认告警方式设置

## API接口

### 更新告警方式配置

**接口地址**: `POST /alert/methods`

**请求体**: JSON格式的告警方式数组

**示例请求**:
```json
[
  {
    "id": 1,
    "name": "email-alert",
    "method_type": "email",
    "description": "邮件告警",
    "is_enabled": 1,
    "is_default": 1,
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.example.com:587",
    "smtp_auth_username": "<EMAIL>",
    "smtp_auth_password": "password",
    "smtp_require_tls": 1,
    "email_to": "<EMAIL>,<EMAIL>",
    "email_subject": "告警通知: {{ .GroupLabels.alertname }}",
    "email_body": "告警详情: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}"
  },
  {
    "id": 2,
    "name": "dingtalk-alert",
    "method_type": "dingtalk",
    "description": "钉钉告警",
    "is_enabled": 1,
    "dingtalk_webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
    "dingtalk_secret": "secret_key",
    "dingtalk_at_mobiles": "13800138000,13900139000",
    "dingtalk_at_all": 0,
    "dingtalk_title": "系统告警",
    "dingtalk_content": "{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}"
  }
]
```

**响应示例**:
```json
{
  "status": "success",
  "message": "Alert methods have been updated successfully"
}
```

## 生成的配置文件示例

根据上述请求，系统会生成如下的Alertmanager配置文件：

```yaml
global:
  smtp_from: <EMAIL>
  smtp_smarthost: smtp.example.com:587
  smtp_auth_username: <EMAIL>
  smtp_auth_password: password
  smtp_require_tls: true

route:
  group_by:
    - alertname
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: default

receivers:
  - name: email-alert
    email_configs:
      - to: <EMAIL>,<EMAIL>
        subject: "告警通知: {{ .GroupLabels.alertname }}"
        body: "告警详情: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}"
  - name: dingtalk-alert
    webhook_configs:
      - url: https://oapi.dingtalk.com/robot/send?access_token=xxx
        send_resolved: true
```

## 配置文件路径

- **生成路径**: `/etc/thestack/conf/alertmanager/alertmanager.yml`
- **热更新端点**: `http://{vip}:9093/-/reload`

## 数据库字段说明

### 通用字段
- `id`: 主键ID
- `name`: 告警方式名称
- `method_type`: 告警方式类型 (email, dingtalk, webhook, sms, wechat)
- `description`: 描述
- `is_enabled`: 是否启用 (1启用, 0禁用)
- `is_default`: 是否为默认告警方式
- `order_id`: 排序ID
- `status`: 状态

### 邮件告警字段
- `smtp_from`: 发件人邮箱
- `smtp_smarthost`: SMTP服务器地址
- `smtp_auth_username`: SMTP认证用户名
- `smtp_auth_password`: SMTP认证密码
- `smtp_require_tls`: 是否需要TLS
- `email_to`: 收件人列表
- `email_subject`: 邮件主题模板
- `email_body`: 邮件内容模板

### 钉钉告警字段
- `dingtalk_webhook_url`: 钉钉机器人Webhook地址
- `dingtalk_secret`: 钉钉机器人加签密钥
- `dingtalk_at_mobiles`: @手机号列表
- `dingtalk_at_all`: 是否@所有人
- `dingtalk_title`: 消息标题模板
- `dingtalk_content`: 消息内容模板

### Webhook告警字段
- `webhook_url`: Webhook地址
- `webhook_method`: HTTP方法
- `webhook_headers`: HTTP请求头
- `webhook_body`: HTTP请求体模板
- `webhook_timeout`: 超时时间

## 使用说明

1. **配置告警方式**: 在数据库中配置告警方式信息
2. **调用同步接口**: 调用 `sync_alert_methods_to_hosts` 函数将配置同步到各主机
3. **自动生成配置**: 系统自动根据配置生成Alertmanager配置文件
4. **热更新**: 自动调用Alertmanager的reload API进行热更新

## 多SMTP服务器支持

### 问题说明

Alertmanager只支持一个全局SMTP配置，当有多个使用不同SMTP服务器的邮件告警方式时，会产生冲突。

### 解决方案

系统采用**混合模式**来解决这个问题：

1. **第一个邮件告警方式**：使用Alertmanager原生邮件配置
2. **后续邮件告警方式**：自动转换为webhook配置，SMTP信息通过HTTP头传递

### 示例配置

假设有3个不同SMTP服务器的邮件告警：

**输入数据**：
```json
[
  {
    "name": "qq-email",
    "method_type": "email",
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.qq.com:587",
    "email_to": "<EMAIL>"
  },
  {
    "name": "gmail-email",
    "method_type": "email",
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.gmail.com:587",
    "email_to": "<EMAIL>"
  },
  {
    "name": "163-email",
    "method_type": "email",
    "smtp_from": "<EMAIL>",
    "smtp_smarthost": "smtp.163.com:25",
    "email_to": "<EMAIL>"
  }
]
```

**生成的配置**：
```yaml
global:
  smtp_from: <EMAIL>
  smtp_smarthost: smtp.qq.com:587
  smtp_auth_username: <EMAIL>
  smtp_auth_password: qqpassword
  smtp_require_tls: true

route:
  group_by: [alertname]
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: default

receivers:
  - name: qq-email
    email_configs:
      - to: <EMAIL>
        subject: QQ邮箱告警
        body: 来自QQ邮箱的告警

  - name: gmail-email
    webhook_configs:
      - url: http://localhost:8080/email-webhook/gmail-email
        send_resolved: true
        http_config:
          headers:
            X-SMTP-From: <EMAIL>
            X-SMTP-Smarthost: smtp.gmail.com:587
            X-SMTP-Username: <EMAIL>
            X-SMTP-Password: gmailpassword
            X-Email-To: <EMAIL>
            X-Email-Subject: Gmail告警
            X-Email-Body: 来自Gmail的告警

  - name: 163-email
    webhook_configs:
      - url: http://localhost:8080/email-webhook/163-email
        send_resolved: true
        http_config:
          headers:
            X-SMTP-From: <EMAIL>
            X-SMTP-Smarthost: smtp.163.com:25
            X-SMTP-Username: <EMAIL>
            X-SMTP-Password: 163password
            X-Email-To: <EMAIL>
            X-Email-Subject: 163邮箱告警
            X-Email-Body: 来自163邮箱的告警
```

### Webhook邮件服务

需要实现一个webhook邮件服务来处理转换后的邮件告警：

```go
// 示例webhook邮件服务
func emailWebhookHandler(w http.ResponseWriter, r *http.Request) {
    // 从HTTP头获取SMTP配置
    smtpFrom := r.Header.Get("X-SMTP-From")
    smtpHost := r.Header.Get("X-SMTP-Smarthost")
    smtpUser := r.Header.Get("X-SMTP-Username")
    smtpPass := r.Header.Get("X-SMTP-Password")

    // 从HTTP头获取邮件信息
    emailTo := r.Header.Get("X-Email-To")
    subject := r.Header.Get("X-Email-Subject")
    body := r.Header.Get("X-Email-Body")

    // 使用对应的SMTP服务器发送邮件
    sendEmail(smtpHost, smtpUser, smtpPass, smtpFrom, emailTo, subject, body)
}
```

## 注意事项

1. 只有 `is_enabled = 1` 的告警方式才会被包含在配置中
2. 如果没有启用的告警方式，系统会创建一个默认的空接收器
3. **多SMTP支持**：第一个邮件告警使用原生配置，后续的自动转换为webhook
4. 钉钉和其他webhook类型的告警都会转换为Alertmanager的webhook配置
5. **webhook邮件服务**：需要部署额外的服务来处理webhook转换的邮件告警
