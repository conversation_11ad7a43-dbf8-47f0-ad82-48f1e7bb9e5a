package main

import (
	"hci_agent/internal/config"
	"hci_agent/internal/database"
	"log"

	"gorm.io/gen"
)

func main() {
	config.InitConfig()
	log.Println(config.Conf.Db)
	db, _ := database.InitDB()
	// 创建生成器
	g := gen.NewGenerator(gen.Config{
		// 指定输出目录
		OutPath:      "./internal/models_methods",
		ModelPkgPath: "./internal/models",
		// 模型文件使用的数据库
		Mode: gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // 生成带默认查询的方法
	})

	// 连接数据库
	g.UseDB(db)

	// 为所有表生成结构体和操作方法
	tables, err := db.Migrator().GetTables()
	if err != nil {
		log.Fatalf("failed to get tables from database: %v", err)
	}

	for _, tableName := range tables {
		// 对于每个表，生成模型和查询方法
		if tableName == "switchs" {
			g.ApplyBasic(g.GenerateModelAs(tableName, "SwitchTable"))

		} else {
			g.ApplyBasic(g.GenerateModel(tableName))

		}

	}

	// 生成代码
	g.Execute()
}
