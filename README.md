

# The Export
## 功能
1. 自动检测磁盘插入，上报插入磁盘信息。

## 使用方法
1. chmod +x build.sh
2. ./build
3. docker 镜像 tag：tianwen1:5000/disk-export:v1


4. docker run -d --name disk-exporter false --net=host --privileged -v /proc:/proc -v /sys:/sys -v /:/rootfs -v /etc:/etc -v /dev:/dev -v /run/lvm:/run/lvm
   {docker image id}

## 待开发
1. 插入旧盘并且不是ceph分区，则不支持，后续开发。
2. Makefile 优化。（已完成）
3. 优化过滤系统盘逻辑。（已完成）
4. 是否运行在docker判断。
5. 增加硬盘大小tag。（已完成）
6. 增加cinder.conf 文件修改.
