package osd

import (
	"context"
	"fmt"
	"log"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/client"
)

func MemoryLeaksRestart(containerName string) {
	ctx := context.Background()
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		log.Printf("Error initializing docker client: %v", err)
		return
	}
	// Get container details
	inspection, err := cli.ContainerInspect(ctx, containerName)
	if err != nil {
		log.Printf("Error inspecting container: %v", err)
		return
	}

	timeout := 10
	stopOptions := container.StopOptions{Timeout: &timeout}
	if err := cli.ContainerStop(ctx, containerName, stopOptions); err != nil {
		log.Printf("Error stopping container: %v", err)
	}

	fmt.Println("Starting container...")
	if err := cli.ContainerStart(ctx, containerName, types.ContainerStartOptions{}); err != nil {
		log.Printf("Error starting container: %v", err)
	}

	// // Check conditions
	// if inspection.State.Status == "running" && inspection.State.Health.Status == "unhealthy" {
	// 	startedTime, err := time.Parse(time.RFC3339Nano, inspection.State.StartedAt)
	// 	if err != nil {
	// 		log.Printf("Error parsing start time: %v", err)
	// 	}

	// 	if time.Since(startedTime) > setting.AppSetting.ContainerTimeOut*time.Minute {
	// 		fmt.Println("Stopping container...")
	// 		timeout := 10
	// 		stopOptions := container.StopOptions{Timeout: &timeout}
	// 		if err := cli.ContainerStop(ctx, containerName, stopOptions); err != nil {
	// 			log.Printf("Error stopping container: %v", err)
	// 		}

	// 		fmt.Println("Starting container...")
	// 		if err := cli.ContainerStart(ctx, containerName, types.ContainerStartOptions{}); err != nil {
	// 			log.Printf("Error starting container: %v", err)
	// 		}
	// 	}
	// }

	//fmt.Printf("%v status: %v  health: %v\n", containerName, inspection.State.Status, inspection.State.Health.Status)
	fmt.Printf("Started at: %v\n", inspection.State.StartedAt)
}
