[DEFAULT]
debug = True
log_dir = /var/log/kolla/cinder
use_forwarded_for = true
use_stderr = False
my_ip = ***************
osapi_volume_workers = 5
volume_name_template = volume-%s
glance_api_servers = http://***************:9292
glance_num_retries = 3
glance_api_version = 2
glance_ca_certificates_file =
os_region_name = RegionOne
enabled_backends = rbd-1,cinder-sdd-controller1
osapi_volume_listen = ***************
osapi_volume_listen_port = 8776
api_paste_config = /etc/cinder/api-paste.ini
auth_strategy = keystone
transport_url = rabbit://openstack:openstack@***************:5672,openstack:openstack@***************:5672,openstack:openstack@***************:5672//

[oslo_messaging_notifications]
transport_url = rabbit://openstack:openstack@***************:5672,openstack:openstack@***************:5672,openstack:openstack@***************:5672//

[oslo_middleware]
enable_proxy_headers_parsing = True

[nova]
interface = internal
auth_url = http://***************:35357
auth_type = password
project_domain_id = default
user_domain_id = default
region_name = RegionOne
project_name = service
username = nova
password = nova
cafile =


[database]
connection = mysql+pymysql://cinder:cinder@127.0.0.1:3306/cinder
connection_recycle_time = 10
max_pool_size = 1
max_retries = -1

[keystone_authtoken]
www_authenticate_uri = http://***************:5000
auth_url = http://***************:35357
auth_type = password
project_domain_id = default
user_domain_id = default
project_name = service
username = cinder
password = cinder
cafile =
memcache_security_strategy = ENCRYPT
memcache_secret_key = memcached
memcached_servers = ***************:11211,***************:11211,***************:11211

[oslo_concurrency]
lock_path = /var/lib/cinder/tmp

[cinder-sdd-controller1]
volume_group = cinder-sdd-controller1
volume_driver = cinder.volume.drivers.lvm.LVMVolumeDriver
volume_backend_name = cinder-sdd-controller1
target_helper = tgtadm
target_protocol = iscsi

