package disk

import (
	"fmt"
	"log"
	"os/exec"
	"strings"

	"gopkg.in/ini.v1"
)

const PATH = "/etc/cinder/cinder.conf"

const PATHDocker = "/etc/kolla/cinder-volume/cinder.conf"

//const PATHDocker = "/home/<USER>/projects/hci_agent/disk/cinder.conf"

func (d *Info) AddConfig(dev string) {
	cfg, _, done := d.initIni()
	if done {
		return
	}
	// 添加section
	sec, err := cfg.NewSection(fmt.Sprintf("cinder-%s-%s", dev, d.Hostname))
	if err != nil {
		log.Println(err)
	}

	// 设置key-value
	sec.Key("volume_driver").SetValue("cinder.volume.drivers.lvm.LVMVolumeDriver")
	sec.Key("volume_group").SetValue(fmt.Sprintf("cinder-%s-%s", dev, d.Hostname))
	sec.Key("target_protocol").SetValue("iscsi")
	sec.Key("target_helper").SetValue("lioadm")
	sec.Key("volume_backend_name").SetValue(fmt.Sprintf("cinder-%s-%s", dev, d.Hostname))

	// 保存INI文件
	if err := cfg.SaveTo(PATHDocker); err != nil {
		log.Println(err)
	}
	d.SetDefaultValue(dev, 1)

	// 重启docker
	err = d.RestartDocker("cinder_volume")
	if err != nil {
		log.Println(err)
	}
}

func (d *Info) initIni() (*ini.File, error, bool) {
	cfg, err := ini.LoadSources(ini.LoadOptions{
		AllowBooleanKeys:            true,
		UnescapeValueDoubleQuotes:   true,
		AllowPythonMultilineValues:  true,
		PreserveSurroundedQuote:     true,
		IgnoreInlineComment:         true,
		UnescapeValueCommentSymbols: true,
		AllowNonUniqueSections:      true,
	}, PATHDocker)
	if err != nil {
		fmt.Println("ini failed :", err)
		return nil, nil, true
	}
	return cfg, err, false
}

func (d *Info) DelConfig(dev string) {
	cfg, err, done := d.initIni()
	if done {
		return
	}
	// 删除指定section
	if cfg.DeleteSection(fmt.Sprintf("cinder-%s-%s", dev, d.Hostname)); err != nil {
		log.Println(err)
	}

	// 保存INI文件
	if err := cfg.SaveTo(PATHDocker); err != nil {
		log.Println(err)
	}
	d.SetDefaultValue(dev, 2)

	// 重启docker
	err = d.RestartDocker("cinder_volume")
	if err != nil {
		log.Println(err)
	}
}

func (d *Info) GetConfig() []string {
	confKeys := make([]string, 0)
	cfg, _, done := d.initIni()
	if done {
		return nil
	}
	sections := cfg.SectionStrings()
	for _, s := range sections {
		if strings.Contains(s, "cinder") {
			suffix := fmt.Sprintf("-%s", d.Hostname) // 后缀是 "-1"

			// 将指定前缀去掉
			result := strings.TrimPrefix(s, "cinder-")
			result = strings.TrimSuffix(result, suffix)
			confKeys = append(confKeys, result)
		}
	}
	return confKeys
}

// RunShell 运行命令行
func (d *Info) RunShell(disk string) {
	cmdPvcreate := exec.Command("pvcreate", fmt.Sprintf("/dev/%s", disk))
	err := cmdPvcreate.Run()
	if err != nil {
		fmt.Println("run cmdPvcreate failed:", err)
	}

	cmdVgcreate := exec.Command("vgcreate", fmt.Sprintf("cinder-%s-%s", disk, d.Hostname), fmt.Sprintf("/dev/%s", disk))
	err = cmdVgcreate.Run()
	if err != nil {
		fmt.Println("run cmdVgcreate failed:", err)
	}
}

func (d *Info) Difference(sliceA, sliceB []string) []string {
	diff := make([]string, 0)

	// 遍历 sliceA 中的元素
	for _, elemA := range sliceA {
		found := false

		// 遍历 sliceB 中的元素，检查是否与 elemA 相同
		for _, elemB := range sliceB {
			if elemA == elemB {
				found = true
				break
			}
		}
		// 如果 elemA 不在 sliceB 中，将其添加到 diff 切片中
		if !found {
			diff = append(diff, elemA)
		}
	}
	return diff
}

func (d *Info) GetVolumeGroupForDisk(diskName string) (string, error) {
	cmd := exec.Command("pvs", "--noheadings", "-o", "vg_name", fmt.Sprintf("/dev/%s", diskName))
	out, err := cmd.Output()
	if err != nil {
		return "", err
	}

	vg := strings.TrimSpace(string(out))
	return vg, nil
}

// SetDefaultValue 设置配置文件 default 值 ， setType 1为增加 ，2为删除
func (d *Info) SetDefaultValue(dev string, setType int) {

	// 1为增加
	if setType == 1 {
		cfg, _, done := d.initIni()
		if done {
			return
		}
		// 修改default 中 enabled_backends 参数
		// 获取要修改的 Section 和 Key
		//section := cfg.Section("DEFAULT1")
		section := cfg.SectionWithIndex("DEFAULT", 1)
		key := section.Key("enabled_backends")
		newEnabledBackends := fmt.Sprintf("%s,%s", key.Value(), fmt.Sprintf("cinder-%s-%s", dev, d.Hostname))
		// 更新
		key.SetValue(newEnabledBackends)
		// 保存INI文件
		if err := cfg.SaveTo(PATHDocker); err != nil {
			log.Println(err)
		}
	} else if setType == 2 {
		cfg, _, done := d.initIni()
		if done {
			return
		}
		// 修改default 中 enabled_backends 参数
		// 获取要修改的 Section 和 Key
		section := cfg.SectionWithIndex("DEFAULT", 1)
		key := section.Key("enabled_backends")
		parts := strings.Split(key.Value(), ",")
		var result []string
		for _, part := range parts {
			if part != fmt.Sprintf("cinder-%s-%s", dev, d.Hostname) {
				result = append(result, part)
			}
		}
		newEnabledBackends := strings.Join(result, ",")

		//更新
		key.SetValue(newEnabledBackends)
		// 保存INI文件
		if err := cfg.SaveTo(PATHDocker); err != nil {
			log.Println(err)
		}
	}
}

func (d *Info) RestartDocker(dockerName string) error {
	cmd := exec.Command("docker", "restart", dockerName)
	_, err := cmd.Output()
	if err != nil {
		return err
	}
	return nil
}
