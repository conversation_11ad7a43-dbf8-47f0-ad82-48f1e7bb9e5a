package disk

import (
	"fmt"
	"testing"
)

func TestInfo_AddConfig(t *testing.T) {
	disk := new(Info)
	fmt.Println(disk.GetConfig())
	disk.AddConfig("s")
	fmt.Println(disk.GetConfig())
}

func TestInfo_DelConfig(t *testing.T) {
	disk := new(Info)
	fmt.Println(disk.GetConfig())
	disk.DelConfig("s")
	fmt.Println(disk.GetConfig())
}

func TestInfo_SetDefaultValue(t *testing.T) {
	disk := new(Info)
	disk.SetDefaultValue("ss", 2)
}
