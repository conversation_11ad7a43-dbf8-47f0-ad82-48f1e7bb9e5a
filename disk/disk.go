package disk

import (
	"encoding/json"
	"fmt"
	"hci_agent/pkg/logging"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var sysPathDocker = "/sys/class/block"
var sysPath = "/sys/class/block"
var sysPathTest = "/root/sheng"

// var devPath = "/dev"
var devPathDocker = "/dev"

type Metrics struct {
	DiskInfo prometheus.GaugeVec
}

type Info struct {
	Hostname    string `json:"hostname"`
	DriveLetter string `json:"drive_letter"` //盘符
}

// GetDiskSignal 获取新插入磁盘信号
func (d *Info) GetDiskSignal(keys *[]string, diskCh chan string) {
	for disk := range diskCh {
		isTrue := d.contains(*keys, disk)
		if isTrue {
			continue
		}
		if !isTrue {
			*keys = append(*keys, disk)
		}
	}
}

// WatchDisksDocker 获取 disk信息 docker啓動
func (d *Info) WatchDisksDocker(diskCh chan<- string) {
	for {
		open, err := os.Open(sysPathDocker)
		if err != nil {
			log.Printf("Error opening %s: %s", sysPathDocker, err.Error())
			time.Sleep(10 * time.Second)
			continue
		}

		files, err := open.Readdirnames(0)
		if err != nil {
			log.Printf("Error reading %s: %s", sysPathDocker, err.Error())
			time.Sleep(10 * time.Second)
			continue
		}

		err = open.Close()
		if err != nil {
			log.Printf("Error closing %s : %s", sysPathDocker, err.Error())
		}

		for _, file := range files {
			syspathdocker := fmt.Sprintf("%s/%s/device", sysPathDocker, file)
			if _, err := os.Stat(syspathdocker); err == nil {
				diskCh <- file
			}
		}
		time.Sleep(20 * time.Second)
	}
}

// WatchDisks 获取 disk信息 系統啓動
func (d *Info) WatchDisks(diskCh chan<- string) {
	for {
		open, err := os.Open(sysPath)
		if err != nil {
			log.Printf("Error opening %s: %s", sysPath, err.Error())
			time.Sleep(10 * time.Second)
			continue
		}

		files, err := open.Readdirnames(0)
		if err != nil {
			log.Printf("Error reading %s: %s", sysPath, err.Error())
			time.Sleep(10 * time.Second)
			continue
		}

		err = open.Close()
		if err != nil {
			log.Printf("Error closing %s : %s", sysPath, err.Error())
		}

		for _, file := range files {
			syspathdocker := fmt.Sprintf("%s/%s/device", sysPath, file)
			if _, err := os.Stat(syspathdocker); err == nil {
				diskCh <- file
			}
		}
		time.Sleep(20 * time.Second)
	}
}

func (d *Info) contains(elems []string, v string) bool {
	for _, s := range elems {
		if v == s {
			return true
		}
	}
	return false
}

// isSystemDisk 检测是否是系统盘
func (d *Info) isSystemDisk(blockDeviceName string) (bool, error) {
	// 执行 lsblk 命令并解析输出
	cmd := exec.Command("lsblk", "-J", fmt.Sprintf("%s/%s", devPathDocker, blockDeviceName))
	out, err := cmd.Output()
	if err != nil {
		return true, err
	}

	var allData struct {
		Blockdevices []struct {
			Name       string `json:"name"`
			MountPoint string `json:"mountpoint"`
			Children   []struct {
				Name       string `json:"name"`
				Type       string `json:"type"`
				MountPoint string `json:"mountpoint"`
			} `json:"children"`
		} `json:"blockdevices"`
	}

	// 解析 JSON 输出
	var data struct {
		Blockdevices []struct {
			Name       string `json:"name"`
			MountPoint string `json:"mountpoint"`
		} `json:"blockdevices"`
	}

	if err := json.Unmarshal(out, &allData); err != nil {
		logging.Log.Error("Error decoding JSON:", err)
		if err = json.Unmarshal(out, &data); err != nil {
			logging.Log.Error("Error decoding JSON:", err)
			return true, err
		}

		// 检查块设备是否是系统盘
		for _, device := range data.Blockdevices {
			if device.Name == blockDeviceName && device.MountPoint == "/" {
				return true, nil
			}
		}
	} else {
		// 检查块设备是否是系统盘
		for _, Blockdevice := range allData.Blockdevices {
			for _, children := range Blockdevice.Children {
				if children.MountPoint == "/" {
					return true, nil
				}
			}
		}
	}
	return false, nil
}

// isCephDisk 检测是否是ceph盘
func (d *Info) isCephDisk(diskName string) (bool, error) {
	// 执行 lsblk 命令并解析输出
	cmd := exec.Command("lsblk", "-o", "name", "-n", "-P", fmt.Sprintf("%s/%s", devPathDocker, diskName))
	out, err := cmd.Output()
	if err != nil {
		return true, err
	}
	output := string(out)
	if strings.Contains(output, "ceph") || strings.Contains(output, "root") {
		return false, nil
	}
	return true, nil
}

func (d *Info) CheckIfCAndCephDrive(deviceName string) (bool, error) {
	isSysDisk, err := d.isSystemDisk(deviceName)
	if err != nil {
		return true, err
	}
	if isSysDisk {
		return true, nil
	} else {
		isCephDisk, err := d.isCephDisk(deviceName)
		if err != nil {
			return true, err
		}
		if isCephDisk {
			return true, nil
		}
		return false, nil
	}
}

// WatchDisksTest 获取disk信息
func (d *Info) WatchDisksTest(diskCh chan<- string) {
	for {
		open, err := os.Open(sysPathTest)
		if err != nil {
			log.Printf("Error opening %s: %s", sysPathTest, err.Error())
			continue
		}

		files, err := open.Readdirnames(0)
		if err != nil {
			log.Printf("Error reading %s: %s", sysPathTest, err.Error())
			continue
		}

		err = open.Close()
		if err != nil {
			log.Printf("Error closing %s : %s", sysPathTest, err.Error())
		}

		for _, file := range files {

			diskCh <- file

		}
	}
}

func (d *Info) GetDiskSize(disk string) string {
	cmd := exec.Command("lsblk", "-b", "-d", "-n", "-o", "SIZE", fmt.Sprintf("%s/%s", devPathDocker, disk))
	output, err := cmd.Output()
	if err != nil {
		fmt.Println(err)
		log.Printf("Error GetDiskSize %s : %s", disk, err.Error())
	}
	sizeBytes := strings.TrimSpace(string(output))
	return sizeBytes
}
